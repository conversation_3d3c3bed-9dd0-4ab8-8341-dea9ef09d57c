<!--
 * @Author: Reaper
 * @Date: 2024-01-30 14:11:18
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-25 16:46:16
 * @Description: 请填写简介
-->
<template>
  <div class="menu-wrapper">
    <template v-for="item in routes" v-show="!item.hidden&&item.children">
      <el-submenu :index="item.name || item.path" :key="item.name " class="submenuOne"> 
        
        <template slot="title">
          <i :class="item.meta_icon" class="menu-icon"></i>
          <span v-if="item.meta_title && item.isRedirect != true" class="menu-title">
            {{ item.meta_title }}
          </span>
          <!-- 判断，当侧边栏展开时，首页 -->
          <router-link
            v-if="item.isRedirect == true && !currentCollapseState"
            :to="item.redirect"
            class="home-link-expanded"
          >
            {{ item.meta_title }}
          </router-link>
        </template>

        <!-- 判断，当侧边栏收起时，首页-->
        <router-link
          v-if="item.isRedirect == true && currentCollapseState"
          :to="item.redirect"
          class="home-link-collapsed"
          :title="item.meta_title"
        >
          <i :class="item.meta_icon"></i>
        </router-link>

        <div v-if="item.isRedirect != true" class="submenu-content">
          <template v-for="child in item.children">
            <!-- 嵌套子菜单 -->
            <sidebar-item
              v-if="child.children && child.children.length > 0"
              :key="'submenu-' + (child.path || child.name)"
              :is-nest="true"
              :is-collapsed="currentCollapseState"
              class="nest-menu"
              :routes="[child]"
            ></sidebar-item>

            <!-- 普通菜单项 -->
            <router-link
              v-else
              :key="'menuitem-' + (child.path || child.name)"
              :to="item.path + '/' + child.path"
              class="menu-item-link"
            >
              <el-menu-item
                :index="item.path + '/' + child.path"
                :title="currentCollapseState ? child.meta_title : ''"
              >
                <span class="menu-item-title">{{ child.meta_title }}</span>
              </el-menu-item>
            </router-link>
          </template>
        </div>


      </el-submenu>
    </template>
  </div>
</template>

<script>
export default {
  name: "sidebarItem",
  props: {
    routes: {
      type: Array,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    isCollapsed: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 获取当前折叠状态
    currentCollapseState() {
      return this.isCollapsed || this.$store.getters.isCollapse;
    }
  },
  methods: {
    // 获取菜单项标题（用于折叠状态下的提示）
    getMenuTitle(item) {
      return item.meta_title || item.name || '';
    }
  },
  created() {
    console.log('sidebar routes:', this.routes);
  },
};
</script>
<style lang="scss" scoped>
.menu-wrapper {
  .submenuOne {
    margin-bottom: 4px;

    .menu-icon {
      font-size: 18px;
      color: #606266;
      transition: color 0.2s ease;
      margin-right: 12px;
    }

    .menu-title {
      font-weight: 500;
      transition: all 0.2s ease;
    }

    // 首页链接样式
    .home-link-expanded {
      color: inherit;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        color: #1677ff;
      }
    }

    .home-link-collapsed {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 48px;
      color: #606266;
      text-decoration: none;
      transition: all 0.2s ease;

      &:hover {
        color: #1677ff;
        background-color: #f0f9ff;
      }

      i {
        font-size: 20px;
      }
    }
  }

  .submenu-content {
    .menu-item-link {
      text-decoration: none;
      color: inherit;

      .menu-item-title {
        font-size: 14px;
        color: #606266;
        transition: color 0.2s ease;
      }
    }
  }

  // 嵌套菜单样式
  .nest-menu {
    .el-submenu__title {
      padding-left: 32px;
    }
  }
}

// 选中项高亮
:deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #1677ff 0%, #69b7ff 100%) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);

  .menu-item-title {
    color: #ffffff !important;
  }
}

:deep(.el-submenu__title.is-active) {
  background: #f0f9ff !important;
  color: #1677ff !important;

  .menu-icon {
    color: #1677ff !important;
  }
}

// 悬浮效果
:deep(.el-menu-item:hover),
:deep(.el-submenu__title:hover) {
  background-color: #f0f9ff !important;
  color: #1677ff !important;

  .menu-icon {
    color: #1677ff !important;
  }

  .menu-item-title {
    color: #1677ff !important;
  }
}
</style>
 
