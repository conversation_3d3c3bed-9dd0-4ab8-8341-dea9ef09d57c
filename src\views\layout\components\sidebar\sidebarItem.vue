<!--
 * @Author: Reaper
 * @Date: 2024-01-30 14:11:18
 * @LastEditors: 
 * @LastEditTime: 2025-06-25 16:34:34
 * @Description: 请填写简介
-->
<template>
  <div class="menu-wrapper">
    <template v-for="item in routes" v-show="!item.hidden&&item.children">
      <el-submenu :index="item.name||item.path" :key="item.name" class="submenuOne"> 
        
        <template slot="title">
          <i :class="item.meta_icon"></i>
          <span v-if="item.meta_title&&item.isRedirect!=true">{{item.meta_title}}</span>
         <!-- 判断，当侧边栏展开时，首页 -->
        <router-link v-if="item.isRedirect==true&&isCollapse==false" :to="item.redirect">{{item.meta_title}}</router-link>
            
        </template>
        <!-- 判断，当侧边栏收起时，首页-->
        <router-link v-if="item.isRedirect==true&&isCollapse==true" :to="item.redirect" style="padding-left: 20px;
    color: #c2c2c2!important;
    background-color: #ffffff!important;font-size: 30px;"><span>{{item.meta_title}}</span></router-link>

        <div v-if="item.isRedirect!=true">
          <template v-for="child in item.children">
            <!--v-if="!child.hidden"-->
            <sidebar-item
              :is-nest="true"
              class="nest-menu"
              v-if="child.children&&child.children.length>0"
              :routes="[child]"
              :key="child.path"
            ></sidebar-item>

            <router-link :to="item.path+'/'+child.path" :key="child.name">
              <!--v-else-->
              <el-menu-item :index="item.path+'/'+child.path">
              <!-- v-if="child.meta_title" -->
                <span  class="alTitle">{{child.meta_title}}</span>
              </el-menu-item>
            </router-link>
          </template>
        </div>


      </el-submenu>
    </template>
  </div>
</template>

<script>
export default {
  name: "sidebarItem",
  props: {
    routes: {
      type: Array
    },
    isNest: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isCollapse:this.$store.getters.isCollapse
    }
  },
  created() {
    // debugger
    console.log('this.routes',this.routes);
  },
};
</script>
 <style lang="scss" scoped>

// 选中项高亮
.el-menu-item.is-active, .el-submenu__title.is-active {
  background: #e6f0ff !important;
  color: #1677ff !important;
}
</style>
 
