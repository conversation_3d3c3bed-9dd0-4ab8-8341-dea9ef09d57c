export default {
    login: '/api/SystemSettingApi/Login',//登录
    GetMenuListById: '/api/SystemSettingApi/GetMenuListById',//获取动态路由   
    GetAdminList: '/api/SystemSettingApi/GetAdminList',//获取管理员列表
    GetRoleList: '/api/SystemSettingApi/GetRoleList',//获取角色列表
    AddAdmin: '/api/SystemSettingApi/AddAdmin',//添加管理员
    UpdateAdmin: '/api/SystemSettingApi/UpdateAdmin',//修改管理员信息
    DeleteAdminByIds: '/api/SystemSettingApi/DeleteAdminByIds',//根据id数组批量删除管理员信息
    AddRole: '/api/SystemSettingApi/AddRole',//添加角色信息
    UpdateRole: '/api/SystemSettingApi/UpdateRole',//修改角色信息
    DeleteRoleByIds: '/api/SystemSettingApi/DeleteRoleByIds',//根据id数组批量删除管理员信息
    GetMenuIdsByRoleId: '/api/SystemSettingApi/GetMenuIdsByRoleId',//根据角色id获取菜单id列表
    SetPower: '/api/SystemSettingApi/SetPower',//设置权限  
    GetGetlncmenu: '/api/TeamNoApi/Getlncmenu',//获取单位列表
    GetMonthofDay: '/api/PersonNoApi/GetMonthofDay',//获取个人体检号源
    GetMonthofDayT: 'api/TeamNoApi/GetMonthofDayT',
    SetPower: '/api/SystemSettingApi/SetPower',//设置权限    
    GetBooknosum: "/api/PersonNoApi/GetBooknosum",//设置预留个检号源
    GetTeamSumnosumm: "/api/TeamNoApi/GetTeamSumnosumm",//设置团体预留号源
    UpdateBooknoTotalTj: "/api/PersonNoApi/UpdateBooknoTotalTTj",//修改个检号源
    UpdateBooknoTotalTTj: "/api/TeamNoApi/UpdateBooknoTotalTj",
    UpdateAllBooknoTotalTj: "/api/TeamNoApi/UpdateAllBooknoTotalTj",
    AddLnc: '/api/BackProtectApi/AddLnc',//新增单位信息
    DeleteById: '/api/BackProtectApi/DeleteById',//删除单位信息
    UpdateLnc: '/api/BackProtectApi/UpdateLnc',//修改单位信息
    UpdateLncContact: '/api/BackProtectApi/UpdateLncContact',//修改单位联系人信息
    GetLncList: '/api/BackProtectApi/GetLncList',//获取所有单位  
    GetUserInfoList: '/api/BackProtectApi/GetAllUserInfo',//获取所有用户信息
    GetNoticeInfo: '/api/BackProtectApi/GetNoticeInfo',//获取体检须知
    UpdateNoticeInfo: '/api/BackProtectApi/UpdateNoticeInfo',//更新体检须知信息
    Timeslot: "/api/TeamNoApi/Timeslot",
    GetTable: '/api/BackProtectApi/GetTable',//导出表格数据
    GetPersonOrder: '/api/BackProtectApi/GetPersonOrder',//获取个检订单
    GetGroupOrder: '/api/BackProtectApi/GetGroupOrder',//获取团检订单
    GetAllOrder: '/api/BackProtectApi/GetAllOrder',//获取全部订单    
    CancelPerOrder: '/api/BackProtectApi/CancelPersonalOrder',//撤销个检订单
    CancelGrpOrder: '/api/BackProtectApi/CancelGroupOrder',//撤销团检订单
    updateBeginTimeConfirm: '/api/BackProtectApi/updateBeginTimeConfirm',//改期
    SyncLnc: '/api/BackProtectApi/SyncLnc',//同步单位
    pay: '/api/PayApi/Getpayy',//获取个检订单
    SendSMSMessagesByOrder: '/api/BackProtectApi/SendSMSMessagesByOrder',//撤销团检订单

    GetAllClusItemComb: '/api/BackProtectApi/GetAllClusItemComb',//获取所有项目
    DeleteCombById: '/api/BackProtectApi/DeleteCombById',//删除项目信息
    UpdateComb: '/api/BackProtectApi/UpdateComb',//修改项目信息
    UpdateCombRelation: '/api/BackProtectApi/UpdateCombRelation',//修改项目信息
    getCombRelation: '/api/BackProtectApi/getCombRelation',//修改项目信息
    UpdateLncIsAllBook: '/api/BackProtectApi/UpdateLncIsAllBook',//修改项目信息
    SyncComb: '/api/BackProtectApi/SyncComb',//同步项目
    GetCombmenu: '/api/CombSumNoApi/GetCombmenu',//获取项目列表
    GetMonthofDayC: 'api/TeamNoApi/GetTJAllMonthofDay',  //获取项目号源总号源
    GetAllMonthofDay: 'api/CombSumNoApi/GetAllMonthofDay',  //获取全部项目号源
    GetCombSumnosumm: "/api/CombSumNoApi/GetCombSumnosumm",//设置项目预留号源
    UpdateBooknoTotalComb: "/api/CombSumNoApi/UpdateBooknoTotalComb",// 修改项目号源
    GetsendSMSRecord: '/api/BackProtectApi/GetsendSMSRecord',//获取短信记录
    GetUserInfo: '/api/BackProtectApi/GetUserInfo',//获取用户信息
    //问卷调查
    GetQuestionnaireList: '/api/QuestionApi/GetQuestionnaireList',//获取问卷调查问题
    GetQuestionnaireDetail: '/api/QuestionApi/GetQuestionnaireDetail',//获取问卷调查详情
    UpdateQuestion: '/api/QuestionApi/UpdateQuestion',//修改调查问卷内容
    AddQuestion: '/api/QuestionApi/AddQuestion',//新增问卷调查问题
    GetAnswerList: '/api/QuestionApi/GetAnswerList',//获取答案列表
    AddAnswerList: '/api/QuestionApi/AddAnswerList',//增加答案列表
    DelAnswer: '/api/QuestionApi/DelAnswer',//删除答案
    GetAnswerListAll: '/api/QuestionApi/GetAnswerListAll',//获取答案列表（所有答案）
    DeleteQuestion: '/api/QuestionApi/DeleteQuestion',//删除问题
    GetWxItems: '/api/QuestionApi/GetWxItems',//获取所有项目
    AddCombs: '/api/QuestionApi/AddCombs',//更新答案关联组合
    GetQuestionLevelList: '/api/QuestionApi/GetQuestionLevelList',//获取问题级别列表
    GetQuestionTypeList: '/api/QuestionApi/GetQuestionTypeList',//获取问题作答方式列表
    // GetQuestionParentList:'/api/QuestionApi/GetQuestionParentList',//根据问题级别获取父级别问题列表
    GetNextQuestionList: '/api/QuestionApi/GetNextQuestionList',//获取下一级问题列表
    //短信模板
    GetAllSMSTemplate: '/api/BackProtectApi/GetAllSMSTemplate',//获取所有项目
    GetAllSMSNoun: '/api/BackProtectApi/GetAllSMSNoun',//获取短信模板默认值
    UpdateSMSTemplate: '/api/BackProtectApi/UpdateSMSTemplate',//更新短信模板
    GetAllSMSTemplateByCode: '/api/BackProtectApi/GetAllSMSTemplateByCode',//根据code获取短信模板
    //套餐管理
    GetAllClusList: '/api/BackProtectApi/GetAllClusList',//获取所有套餐
    GetClusList: '/api/BackProtectApi/GetClusList',//根据单位编号获取套餐
    UpdateClus: '/api/BackProtectApi/UpdateClus',//更新套餐
    SyncClus: '/api/BackProtectApi/SyncPerClus',//同步套餐
    GetAddClusItemList: '/api/BackProtectApi/GetAddClusItemList',
    UpdateClusInfoAndComb: '/api/BackProtectApi/UpdateClusInfoAndComb',
    AddClusInfoAndComb: '/api/BackProtectApi/AddClusInfoAndComb',
    GetClusItemCombByCode: '/api/BackProtectApi/GetClusItemCombByCode', //根据套餐编号获取套餐项目
    DeleteClusById: '/api/BackProtectApi/DeleteClusById',//删除套餐信息
    GetTeamClusItemList:'/api/UserInfoApi/GetTeamClusItemList',//获取团体体检套餐
    SyncOrderByOrderIds: '/api/BackProtectApi/SyncOrderByOrderIds',//根据订单将用户信息导入内院
    //客户名单
    GetCluserByClusCode:"/api/TeamListApi/GetCluserByClusCode",
    GetTeamList: '/api/TeamListApi/GetTeamList',  //获取客户名单
    AddTeamList: '/api/TeamListApi/AddTeamList',  //导入客户
    addSingleCustomer: '/api/TeamListApi/addSingleCustomer', //单价单个客户
    updateSingleCustomer: '/api/TeamListApi/updateSingleCustomer', //更新客户名单
    RevokeAtnTeamList: '/api/TeamListApi/RevokeAtnTeamList', //撤销激活
    DeleteTeamListById: '/api/TeamListApi/DeleteTeamListById',//删除客户名单
    activationTeaamList: '/api/TeamListApi/activationTeaamList',//激活客户名单
    SendSMSMessagesByTeam: '/api/TeamListApi/SendSMSMessagesByTeam',//发送短信（名单表）
    GetPersonSumList:'/api/PersonSumApi/GetPersonSumList',//获取个人号源
    GetSumTimeList:'/api/PersonSumApi/GetSumTimeList',//获取个人时段数据   
    GetTeamSumList:'/api/TeamSumApi/GetTeamSumList',//获取团体号源
    GetTeamSumTimeList:'/api/TeamSumApi/GetTeamSumTimeList',//获取团体时段数据
    DYYTeaamList:'/api/TeamListApi/DYYTeaamList',//获取团体时段数据
    GetAllTeamList:'/api/TeamListApi/GetAllTeamList', //获取用户名单

    GetNewAllTeamList:'/api/TeamListApi/GetNewAllTeamList',
    GetTeamListToTel:'/api/TeamListApi/GetTeamListToTel',  
    // SendReminderMessages:'/api/TeamListApi/SendReminderMessages', //发送消息模板
    updateValidityPeriod:'/api/TeamListApi/updateValidityPeriod',//更新用户有效期
    ClusReorder:'/api/BackProtectApi/ClusReorder',//套餐排序
    CopyClus:'/api/BackProtectApi/CopyClus',//复制套餐
    AddSMSTemplate:'/api/BackProtectApi/AddSMSTemplate', //添加消息模板

    ClusGotItClick: '/api/BackProtectApi/ClusGotItClick',//套餐提示点击知道了按钮

    GetClassificationList:'/api/BackProtectApi/GetClassificationList', //查询项目分类
    AddClassification:'/api/BackProtectApi/AddClassification', //添加项目分类
    UpdateClassification:'/api/BackProtectApi/UpdateClassification', //编辑项目分类
    DeleteClassificationById:'/api/BackProtectApi/DeleteClassificationById', //删除项目分类

    GetTJAllMonthofDay:"api/TeamNoApi/GetTJAllMonthofDay",
    UpdateTJAllBooknoTotalTj:"api/TeamNoApi/UpdateTJAllBooknoTotalTj",
    GetTjAllSumnosumm:"api/TeamNoApi/GetTjAllSumnosumm",

    GetAddCombination: '/api/BackProtectApi/GetAddCombination',//获取加项包
    AddToAddCombination: '/api/BackProtectApi/AddToAddCombination',//添加加项包
    DeleteAddCombinationById: '/api/BackProtectApi/DeleteAddCombinationById',//删除加项包
    updateAddCombination: '/api/BackProtectApi/updateAddCombination',//更新加项包
    addOrEditAddCombistItemList: '/api/BackProtectApi/addOrEditAddCombistItemList',//更新组合项目
    getAddCombistItemList: '/api/BackProtectApi/getAddCombistItemList',//获取组合项目
    GetActivateRecordList:'/api/TeamListApi/GetActivateRecordList',//激活记录

    GetaddPackageByCode:'/api/BackProtectApi/GetaddPackageByCode',//激活记录

    GetNumberOfMeals:'/api/OrderListApi/GetNumberOfMeals', //备餐数量

    HttpWebClientTest:'/api/Test/HttpWebClientTest',

    GetGroupSum:'/api/BackProtectApi/GetGroupSum',//获取团检预约信息
    GetPersonSum:'/api/BackProtectApi/GetPersonSum',//获取个检预约信息
    GetOrderList:'/api/BackProtectApi/GetOrderList',//获取订单信息
    GetOrderListByContainsComb:'/api/BackProtectApi/GetOrderListByContainsComb',//获取彩超信息
    GetCombSum:'/api/BackProtectApi/GetCombSum',//获取彩超信息
    
}