/*
 * @Author: 536584936 <EMAIL>
 * @Date: 2024-01-30 14:11:18
 * @LastEditors: 536584936 <EMAIL>
 * @LastEditTime: 2024-11-27 14:31:33
 * @FilePath: \龙岗中医院后台管理\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)
const fixedRouter=[
  {
    path: '/login',
    name: 'login',
    component: (resolve) => require(['../views/Login/login.vue'], resolve)
  },

  {
    path: '/Test',
    name: 'Test',
    component: (resolve) => require(['../views/Test.vue'], resolve)
  }


]

//这个是解决多次点击跳转同一个页面报错的BUG；
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err) 
}

export default new VueRouter({
  routes: fixedRouter
})