<template>
    <div>
      <div class="clusDiv">
        <el-container style="width: 100%">
          <el-header>
            <div style="width: 100%; margin-top: 10px;">
              <el-button @click="goBackEvent">返回</el-button>
            </div>
            <div class="container-header">
              <el-form
                class="container-header-form"
                :inline="true"
                :label-position="'right'"
                label-width="80px"
              >
                <el-form-item label="套餐名称">
                  <el-input
                    v-model="ClusList.clus_Name"
                    placeholder="请输入套餐名称"
                    size="small"
                  ></el-input>
                </el-form-item>
                <el-form-item label="套餐售价">
                  <el-input
                    v-model="ClusList.price"
                    placeholder="请输入套餐价格"
                    size="small"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-checkbox v-model="ClusList.lockPrice">锁定金额</el-checkbox>
                </el-form-item>
                <el-form-item label="性别">
                  <el-select v-model="ClusList.clus_sex" size="small">
                    <el-option label="不限" value="%"></el-option>
                    <el-option label="男" value="1"></el-option>
                    <el-option label="女" value="0"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="是否启用">
                  <el-select v-model="ClusList.state" size="small">
                    <el-option label="启用" value="T"></el-option>
                    <el-option label="禁用" value="F"></el-option>
                  </el-select>
                </el-form-item>
                <div></div>
                <el-form-item label="业务类型">
                  <el-select v-model="ClusList.business" size="small">
                    <el-option
                      v-for="item in YWoptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="体检类别">
                  <el-select v-model="ClusList.category" size="small">
                    <el-option
                      v-for="item in LBoptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="改项限制">
                  <el-select v-model="ClusList.isModify" size="small">
                    <el-option label="允许加项" value="T"></el-option>
                    <el-option label="允许加指定项目" value="N"></el-option>
                    <el-option label="不允许加项" value="F"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="限制金额" v-if="ClusList.isModify != 'F'">
                  <el-input
                    v-model="ClusList.maxPrice"
                    placeholder="请输入限制金额"
                    size="small"
                  ></el-input>
                </el-form-item>
                <div></div>
                <el-form-item label="套餐分类">
                <el-select v-model="ClusList.clusType" size="small">
                  <el-option
                    label="个人健康体检"
                    value="01"
                  ></el-option>
                  <el-option
                    label="入职及其他"
                    value="06"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="年龄限制">
                <el-select v-model="ClusList.ageIimit" size="small">
                  <el-option
                    label="启用"
                    value="T"
                  ></el-option>
                  <el-option
                    label="禁用"
                    value="F"
                  ></el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="限制范围" v-if="ClusList.ageIimit == 'T'">
                  <el-input v-model="ClusList.minAge" placeholder="最小年龄" type="number"  style="width: 100px;"></el-input>  
                  <span>至</span>
                  <el-input v-model="ClusList.maxAge" placeholder="最大年龄" type="number"  style="width: 100px;"></el-input>
                </el-form-item>
                <div></div>
                <el-form-item label="套餐简介">
                  <el-input
                    type="textarea"
                    :rows="4"
                    v-model="ClusList.clus_Note"
                    placeholder="请输入套餐简介"
                  ></el-input>
                </el-form-item>
                <div></div>
                <el-form-item label="注意事项">
                  <editClusTinymce v-model="ClusList.Notice"/>
                </el-form-item>
              </el-form>
              <div slot="footer">
                <div style="float: right; width: 200px">
                  <el-button type="info" @click="ExportExcel">导出套餐</el-button>
                  <el-button type="primary" @click="saveClusInfo" style="margin-bottom: 10px">保存</el-button>
                </div>
              </div>
            </div>
          </el-header>
          <el-container>
            <el-aside width="350px">
              <el-main width="350px">
                <el-tabs v-model="activeName" @tab-click="handleCombClick">
                  <el-tab-pane label="套餐项目" name="first"></el-tab-pane>
                  <el-tab-pane
                    label="加项"
                    name="second"
                    v-if="ClusList.isModify == 'N'"
                  ></el-tab-pane>
                </el-tabs>
                <div class="asideAll" v-if="activeName == 'first'">
                  <div class="asideSelect">
                    <div>
                      套餐内共{{ checkedCities.length }}个项目，合计{{
                        price.toFixed(2)
                      }}元
                    </div>
                  </div>
                  <div class="asideTitle">
                    <div class="asideMaLeft">
                      <div class="asideAStyle">
                        <div class="asideAName">项目名称</div>
                        <div class="asideAPrice">价格</div>
                      </div>
                    </div>
                  </div>
                  <div v-infinite-scroll="load" style="overflow: auto">
                    <div v-for="checked in checkedCities" :key="checked.id">
                      <div class="asideACombItem">
                        <div class="asideAStyle">
                          <div
                            class="asideicon-view"
                            @click="isViewPackage(checked)"
                          >
                            <div class="viewContainer">
                              <i class="el-icon-view"
                                ><div
                                  v-if="checked.isView == 'F'"
                                  class="asideicon-onView"
                                ></div
                              ></i>
                            </div>
                          </div>
                          <div class="asideAName">
                            <el-tooltip  placement="top" effect="light">
                            <div slot="content" style="font-size: 16px;">{{ checked.comb_Name }}</div>
                            <span>{{ checked.comb_Name }}</span>
                          </el-tooltip>
                            <!-- {{ checked.comb_Name }} -->
                          </div>
                          <div class="asideAPrice">
                            {{ checked.comb_Price.toFixed(2) }}
                          </div>
                          <div class="asideDelete" @click="addPackage(checked)">
                            <i class="el-icon-delete"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="asideAll" v-if="activeName == 'second'">
                  <div class="asideSelect">
                    <div>套餐可加项目：</div>
                  </div>
                  <div class="asideTitle">
                    <div class="asideMaLeft">
                      <div class="asideAStyle">
                        <div class="asideAName">项目名称</div>
                        <div class="asideAPrice">价格</div>
                      </div>
                    </div>
                  </div>
                  <div v-infinite-scroll="load" style="overflow: auto">
                    <div v-for="checked in allowAddComb" :key="checked.id">
                      <div class="asideACombItem">
                        <div class="asideAStyle">
                          <div
                            class="asideicon-view"
                            @click="isViewPackage(checked)"
                          ></div>
                          <div class="asideAName">
                            <el-tooltip  placement="top" effect="light">
                              <div slot="content" style="font-size: 16px;">{{ checked.comb_Name }}</div>
                              <span>{{ checked.comb_Name }}</span>
                            </el-tooltip>
                            <!-- {{ checked.comb_Name }} -->
                          </div>
                          <div class="asideAPrice">
                            {{ checked.comb_Price.toFixed(2) }}
                          </div>
                          <div class="asideDelete" @click="addPackage(checked)">
                            <i class="el-icon-delete"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-main>
            </el-aside>
            <el-main>
              <div class="CombHeader">
                <div class="lncTop">
                  <el-input
                    placeholder="项目名称(搜索所有项目)"
                    style="width: 240px"
                    v-model="CombName"
                    @input="screenCombData"
                  ></el-input>
                </div>
              </div>
              <div class="textDiv" v-for="(item, index) in CombData" :key="index">
                <div class="textDivItem">{{ item.text }}</div>
                <div class="CombAll">
                  <div
                    class="CombAllDiv"
                    v-for="Comb in item.children"
                    :key="Comb.id"
                  >
                    <div class="CombItem" @click="addPackage(Comb)" :class="switchHaoyuanClass(Comb)">
                      <div class="CombItemStyle">
                        <div class="checkeDiv">
                          <i class="el-icon-check"></i>
                        </div>
                        <div class="CombItemName">
                          <el-tooltip  placement="top" effect="light">
                            <div slot="content" style="font-size: 16px;">{{ Comb.comb_Name }}</div>
                            <span>{{ Comb.comb_Name }}</span>
                          </el-tooltip>
                         </div>
                        <div class="CombItemPrice">{{ Comb.comb_Price }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-main>
          </el-container>
        </el-container>
      </div>
      <!--新增/编辑对话框-->
      <el-dialog title="提示" width="20%" :visible.sync="dialogVisible">
        <div>
          <div class="combDiv">
            <div>你选择的项目:</div>
            <div class="combNameDialog">
              {{ msgData.selectCombName.comb_Name }}
            </div>
            <div>依赖以下项目:</div>
            <div v-for="(item, index) in msgData.ComDataList" :key="index">
              <div class="combNameDialog">
                {{ item.comb_Name }}
              </div>
            </div>
            <div>是否全部选择?</div>
          </div>
        </div>
        <div slot="footer">
          <el-button type="danger" size="small" @click="dialogVisible = false"
            >全都不选</el-button
          >
          <el-button type="primary" size="small" @click="selectBDCom"
            >全选</el-button
          >
        </div>
      </el-dialog>
      <!--项目互斥对话框-->
      <el-dialog title="提示" width="20%" :visible.sync="MEdialogVisible" center>
        <div>
          <div class="combDiv">
            <div>你选择的项目:</div>
            <div class="combNameDialog">
              {{ msgData.selectCombName.comb_Name }}
            </div>
            <div>与以下项目互斥:</div>
            <div v-for="(item, index) in msgData.ComDataList" :key="index">
              <div class="combNameDialog">
                {{ item.comb_Name }}
              </div>
            </div>
            <div>无法选择</div>
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" size="small" @click="MEdialogVisible = false"
            >知道了</el-button
          >
        </div>
      </el-dialog>
      <!--项目被依赖对话框-->
      <el-dialog title="提示" width="20%" :visible.sync="BDdialogVisible" center>
        <div>
          <div class="combDiv">
            <div>你选择的项目:</div>
            <div class="combNameDialog">
              {{ msgData.selectCombName.comb_Name }}
            </div>
            <div>被以下项目依赖</div>
            <div v-for="(item, index) in msgData.ComDataList" :key="index">
              <div class="combNameDialog">
                {{ item.comb_Name }}
              </div>
            </div>
            <div>无法取消</div>
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" size="small" @click="BDdialogVisible = false"
            >知道了</el-button
          >
        </div>
      </el-dialog>
      <!--项目被依赖对话框-->
      <el-dialog title="提示" width="20%" :visible.sync="ErrordialogVisible" center>
        <div>
          <div class="combDiv">
            <div>以下项目出现异常</div>
            <div v-for="(item, index) in errComb" :key="index">
              <div class="combNameDialog">
                {{ item.comb_Name }}
              </div>
            </div>
            <div><span>异常类型：</span><span style="color: rgb(89, 0, 255);">项目性别类型与套餐性别类型冲突</span></div>
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" size="small" @click="ErrordialogVisible = false"
            >知道了</el-button
          >
        </div>
      </el-dialog>
    </div>
  </template>
    
    <script>
  import temporaryData from "../../common/temporaryData.js"; //号源临时假数据
  import { ajax } from "../../common/ajax";
  import { toolsUtils } from "../../common/toolsUtils";
  import apiUrls from "../../config/apiUrls";
  import { storage } from "@/common";
  import editClusTinymce from "./editClusTinymce.vue";
  export default {
    name: "BookTj",
    components: { editClusTinymce },
    props:{
        lnc_Code:{
            typeof:String,
            default:''
        }
    },
    data() {
      return {
        drawer: false, //切换号源抽屉
        darwerValue: "所有单位", //号源值
        drawerIpnut: "", //单位编码名称
        hoverIndex: -1, //表示当前hover的是第几个div 初始为 -1 或 null 不能为0 0表示第一个div
        sourceValue: "总号源",
        everyWidth: "width:calc( 100% / 16)",
        height: "calc( 100vh - 350px)",
        // lnc_Code: "",
        drawerData: [], //单位列表
        ContactList: [{}],
        CombAllData: [],
        CombData: [],
        checkboxGroup1: [],
        lnc_Name: "",
        checkedAllCities: [],
        checkedCities: [],
        selectCombList: [],
        ids: "", //id集合 用于批量删除或单个删除
        tableData: [], //表数据
        tableCopyTableList: [], //表数据集合
        tableConstData: [], //存放数据 用于筛选数据
        index: 1, //当前页数
        size: 50, //页码
        dialogVisible: false, //新增编辑对话框是否显示
        dialogTitle: "", //对话框的标题
        BusinessType: "",
        loading: false,
        isUpdate: false,
        //套餐模型
        ClusList: {
          id: "",
          clus_Code: "",
          clus_Name: "",
          state: "",
          price: "",
          isModify: "",
          maxPrice: 0,
          clus_sex: "",
          clus_Note: "",
          category: "",
          business: "",
          ageIimit:"",
          maxAge:"",
          minAge:"",
          clusType: "",
          Notice:"",
          lockPrice: false,
        },
        drawer: false,
        direction: "rtl",
        CombName: "",
        checkedCombName: "",
        price: 0,
        activeName: "first",
        allowAddComb: [],
        LBoptions: [
          {
            value: "01",
            label: "入职体检",
          },
          {
            value: "02",
            label: "健康证体检",
          },
          {
            value: "03",
            label: "驾驶员体检",
          },
          {
            value: "04",
            label: "特种行业",
          },
          {
            value: "05",
            label: "单项体检",
          },
          {
            value: "06",
            label: "只抽血",
          },
          {
            value: "10",
            label: "从业人员体检(食品)",
          },
          {
            value: "11",
            label: "普通招工体检",
          },
          {
            value: "17",
            label: "从业人员体检(药检)",
          },
          {
            value: "18",
            label: "医生注册",
          },
          {
            value: "19",
            label: "护士注册",
          },
          {
            value: "20",
            label: "入户体检",
          },
          {
            value: "21",
            label: "个人套餐",
          },
          {
            value: "22",
            label: "其他体检",
          },
          {
            value: "23",
            label: "人事局招调体检",
          },
          {
            value: "24",
            label: "优才公司体检",
          },
          {
            value: "26",
            label: "从业人员体检(特种行业)",
          },
          {
            value: "27",
            label: "从业人员体检(公共场所)",
          },
        ],
        YWoptions: [
          {
            value: "01",
            label: "健康体检",
          },
          {
            value: "02",
            label: "职业体检",
          },
          {
            value: "03",
            label: "从业体检",
          },
          {
            value: "04",
            label: "招工体检",
          },
          {
            value: "05",
            label: "学生体检",
          },
          {
            value: "06",
            label: "征兵体检",
          },
          {
            value: "07",
            label: "其他体检",
          },
        ],
        JSoptions: [
          {
            value: "2",
            label: "个人",
          },
          {
            value: "1",
            label: "单位",
          },
        ],
        msgData: {
          selectCombName: "",
          ComDataList: [],
          TZCombs: [],
        },
        MEdialogVisible: false,
        BDdialogVisible: false,
        errComb:[],
        ErrordialogVisible:false,
      };
    },
    created() {
      // this.loadBtn();
      // var lncList = JSON.parse(storage.session.get("lncList"));
      // if (lncList == null) {
      //   this.drawer = true;
      // } else {
      //   this.lnc_Code = lncList.lnc_Code;
      //   this.lnc_Name = lncList.lnc_Name;
        // this.GetClusList();
      // }
      console.log(this.lnc_Code);
      this.GetAllClusItemComb();
      //this.dateEvent();
    },
    methods: {
      load() {},
      //获取选中行id
      handleSelectionChangePeople(rows) {
        this.ids = rows.map((row) => row.id);
      },
    //   validate() {  
    //   if (this.ClusList.minAge > this.ClusList.maxAge) {  
    //     this.$message.warning("最小年龄不能大于最大年龄");
    //   }
    // }, 
      //导出套餐
      ExportExcel(){
          let ids = [this.ClusList.id];
          console.log(ids);
          if (ids.length == 0) {
            this.$message.warning("请选择套餐");
            return;
          }
          var pData = {
              ids: ids,
            };
          // var pdfData = toolsUtils.encrypt(this.ClusList.id);
          var pdfData = encodeURIComponent(
              toolsUtils.encrypt(JSON.stringify(pData) + "/" + "excel@2023")
            );
          setTimeout(function () {
              //原因是window.open会中断正在执行的进程，这样能保证其它代码执行完成再执行这个。
              window.location.href ="http://192.168.240.103:36889"+ "/home/<USER>" + pdfData; //改变页面的location  
            }, 300);
      },
      checkSaveClusInfo() {
        if (!this.ClusList.clus_Name) {
          this.$message.warning("请输入套餐名称");
          return false;
        }
        if (!this.ClusList.price) {
          // this.$message.warning("请输入套餐价格");
          // return false;
          this.ClusList.price=0;
        }
        if (this.ClusList.isModify != "F") {
          if (!this.ClusList.maxPrice || this.ClusList.maxPrice == 0) {
            this.$message.warning("请输入限制金额");
            return false;
          }
          if (
            parseFloat(this.ClusList.price) > parseFloat(this.ClusList.maxPrice)
          ) {
            this.$message.warning("限制金额不能小于套餐金额");
            return false;
          }
        }
        if (this.checkedCities.length == 0) {
          this.$message.warning("请输入套餐项目");
          return false;
        }
        if (this.ClusList.isModify == "N" && this.allowAddComb.length == 0) {
          this.$message.warning("请选择加项项目");
          return false;
        }
        if (!this.ClusList.clus_sex) {
          this.$message.warning("请选择性别");
          return false;
        }
        console.log(this.ClusList.clusType);
        if (!this.ClusList.clusType) {
          this.$message.warning("请选择套餐分类");
          return false;
        }
        switch (this.ClusList.clusType) {
              case "个人健康体检":
              this.ClusList.clusType = "01";
                break;
              case "入职及其他":
              this.ClusList.clusType = "06";
                break;
              default:
                break;
            }
        if (this.ClusList.clus_sex != "%") {
          let errComb=[];
          for (let i = 0; i < this.checkedCities.length; i++) {
            if (this.checkedCities[i].sex !=this.ClusList.clus_sex && this.checkedCities[i].sex !="%") {
              errComb.push(this.checkedCities[i]);
            }
          }
          if (errComb.length > 0) {
            this.errComb=errComb;
            this.ErrordialogVisible=true;
            return false;
          }
        }
        if (!this.ClusList.category) {
          this.$message.warning("请选择类别");
          return false;
        }
        if (!this.ClusList.business) {
          this.$message.warning("请选择业务类型");
          return false;
        }
        if (this.ClusList.ageIimit=="T") {
          if  (!this.ClusList.minAge||!this.ClusList.maxAge) {
            this.$message.warning("请输入限制范围");
            return false;
          }
          if (parseInt(this.ClusList.minAge)>parseInt(this.ClusList.maxAge)) {
            this.$message.warning("最小年龄大于最大年龄，请重新输入");  
            return false;
          }
        }
        return true;
      },
      saveClusInfo() {
        this.overallLoading = this.$loading({
          lock: true,
          text: "正在保存中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        //参数验证
        if (!this.checkSaveClusInfo()) {
          this.overallLoading.close();
          return;
        }
        var addOrEdit = ""; //请求变量 新增/编辑 api地址不一样
        if (this.BusinessType == "Edit") {
          addOrEdit = apiUrls.UpdateClusInfoAndComb;
        } else {
          this.ClusList.id = 0;
          addOrEdit = apiUrls.AddClusInfoAndComb;
        }
        var pData = {
          data: {
            lnc_Code: this.lnc_Code,
          },
          clusterHelper: {
            cluster: this.ClusList,
            combList: this.checkedCities,
            addCombList: this.allowAddComb,
          },
        };
        console.log(pData);
        ajax
          .post(addOrEdit, pData)
          .then((r) => {
            this.overallLoading.close();
            if (!r.data.success) {
              alert(r.data.returnMsg);
              return;
            }
            this.$message.success("保存成功");
            this.isUpdate = false; //成功后关闭对话框
            // this.GetClusList(); //重新加载
            this.$parent.GetClusList();
            this.$parent.editFlag = false;
          })
          .catch((err) => {
            this.overallLoading.close();
            this.$message.error("系统繁忙！请稍后再试");
          });
      },
      goBackEvent() {
        this.$parent.editFlag = false;
        // if (
        //   (this.ClusList.clus_Name || this.checkedCities.length != 0) &&
        //   this.BusinessType == "Add"
        // ) {
        //   let confirmMsg = "是否将现有信息缓存?";
        //   this.$confirm(confirmMsg, "提示", {
        //     confirmButtonText: "确定",
        //     cancelButtonText: "取消",
        //     type: "info",
        //   })
        //     .then(() => {
        //       storage.session.set(
        //         "clusInfo",
        //         JSON.stringify({
        //           cluster: this.ClusList,
        //           combList: this.checkedCities,
        //           addCombList: this.allowAddComb,
        //         })
        //       );
        //       this.$parent.editFlag = false;
        //     })
        //     .catch((err) => {
        //       this.$parent.editFlag = false;
        //     });
        // } else {
        //   this.$parent.editFlag = false;
        // }
      },
      //显示模态框
      showAddorEditDialog(row,copy=false) {
        if (row == undefined && !copy) {
          this.dialogTitle = "新增套餐";
          this.BusinessType = "Add";
          var clusInfo = JSON.parse(storage.session.get("clusInfo"));
          console.log(clusInfo);
        } 
        if(row != undefined && !copy){
          this.dialogTitle = "查看/编辑套餐";
          this.BusinessType = "Edit";
          this.GetClusItemComb(row.clus_Code);
        }
        if (row != undefined && copy) {
          this.dialogTitle = "新增套餐";
          this.BusinessType = "Add";
          this.GetClusItemComb(row.clus_Code);
          row = {
            ...row,
            clus_Code:'',
            clus_Name:row.clus_Name+'复制'
          }
        }
        console.log(row);
        this.allowAddComb = [];
        this.checkedCities = [];
        this.price = 0;
        this.ClusList.id = row ? row.id : "";
        this.ClusList.clus_Code = row ? row.clus_Code : "";
        this.ClusList.clus_Name = row ? row.clus_Name : "";
        this.ClusList.clus_sex = row
          ? row.clus_sex != "不限"
            ? row.clus_sex == "男"
              ? "1"
              : "0"
            : "%"
          : "%";
        this.ClusList.isModify = row
          ? row.isModify != "不允许加项"
            ? row.isModify == "允许加指定项目"
              ? "N"
              : "T"
            : "F"
          : "F";
        this.ClusList.category = row ? row.category : "01";
        this.ClusList.business = row ? row.business : "01";
        this.ClusList.price = row ? row.price : "";
        this.ClusList.clus_Note = row ? row.clus_Note : "";
        this.ClusList.maxPrice = row ? row.maxPrice : "0";
        this.ClusList.state = (row ? row.state : "启用") == "启用" ? "T" : "F";
        this.ClusList.clusType = row ? row.clusType : "";
        this.ClusList.ageIimit = (row ? row.ageIimit : "启用") == "启用" ? "T" : "F";
        this.ClusList.minAge = row ? row.minAge : "";
        this.ClusList.maxAge = row ? row.maxAge : "";
        this.ClusList.Notice = row ? row.notice : "";
        this.ClusList.lockPrice = row ? row.lockPrice : false;
        this.activeName = "first";
        this.isUpdate = true;
        // this.drawer = true;
      },
      // 页数改变事件
      handleSizeChange(size) {
        this.size = size;
        this.tableData = this.paging(size, this.index);
      },
      // 页码改变事件
      handleCurrentChange(current) {
        this.index = current;
        this.tableData = this.paging(this.size, current);
      },
      // 本地分页的方法
      paging(size, current) {
        const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
        const tablePush = [];
        tableList.forEach((item, index) => {
          if (size * (current - 1) <= index && index <= size * current - 1) {
            tablePush.push(item);
          }
        });
        return tablePush;
      },
      //获取所有套餐信息
      GetClusList() {
        var that = this;
        that.loading = false;
        var pData = {
            lnc_Code: this.lnc_Code,
        };
        ajax
          .post(apiUrls.GetClusList, pData, { nocrypt: true })
          .then((r) => {
            if (!r.data.success) {
              alert(r.data.returnMsg);
              return;
            }
  
            // 初始化数据
            that.tableConstData = r.data.returnData;
            that.tableCopyTableList = r.data.returnData.map((val) => {
              switch (val.clus_sex) {
                case "1":
                  val.clus_sex = "男";
                  break;
                case "0":
                  val.clus_sex = "女";
                  break;
                case "%":
                  val.clus_sex = "不限";
                  break;
                default:
                  break;
              }
              switch (val.clusType) {
              case "01":
                val.clusType = "个人健康体检";
                break;
              case "06":
                val.clusType = "入职及其他";
                break;
              default:
                break;
            }
              return val;
            });
            that.tableData = that.paging(that.size, that.index);
          })
          .catch((err) => {
            alert("获取套餐失败,请稍后重试");
          });
      },
      //删除套餐
      DeleteClus(ids) {
        let idArr = [];
        if (ids) {
          idArr = [ids];
        } else {
          idArr = this.ids;
        }
        if (idArr.length == 0) {
          this.$message.warning("请选择套餐");
          return;
        }
  
        this.$confirm("确定删除此套餐吗, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "error",
        }).then(() => {
          let pData = {
            data: {
              ids: idArr,
            },
          };
          ajax
            .post(apiUrls.DeleteClusById, pData)
            .then((r) => {
              if (!r.data.success) {
                this.$message.error(r.data.returnMsg);
                return;
              }
              this.$message.success("删除成功");
              // this.GetClusList();
            })
            .catch((err) => {
              console.log(err);
              debugger;
              this.$message.error("系统繁忙！请稍后再试");
            });
        });
      },
      //检查输入的参数
      checkAdminInfo() {
        if (!this.ClusList.clus_Code) {
          this.$message.warning("请输入套餐编码");
          return false;
        }
        if (!this.ClusList.clus_Name) {
          this.$message.warning("请输入套餐名称");
          return false;
        }
        if (this.ClusList.clus_sex == "男") {
          this.ClusList.clus_sex = "1";
        } else if (this.ClusList.clus_sex == "女") {
          this.ClusList.clus_sex = "0";
        } else {
          this.ClusList.clus_sex = "%";
        }
        return true;
      },
      //新增或者修改套餐
      addOrEditClus() {
        //参数验证
        if (!this.checkAdminInfo()) {
          return;
        }
        var pData = {
          cluster: this.ClusList,
        };
        ajax
          .post(apiUrls.UpdateClus, pData)
          .then((r) => {
            if (!r.data.success) {
              alert(r.data.returnMsg);
              return;
            }
            this.$message.success("操作成功");
            // this.GetClusList(); //重新加载
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      },
      //表格筛选
      GetNewData() {
        this.tableCopyTableList = this.tableConstData.filter((item) => {
          //筛选
          return (
            !this.ClusList.clus_Name ||
            item.clus_Name.includes(this.ClusList.clus_Name)
          );
        });
        this.tableData = this.paging(this.size, this.index);
      },
      handleClose(done) {
        this.$confirm("确认关闭？")
          .then((_) => {
            done();
          })
          .catch((_) => {});
      },
      querys() {
        this.loadBtn();
      },
      loadBtn() {
        var pData = {
            kw: this.drawerIpnut,
        };
        ajax
          .post(apiUrls.GetGetlncmenu, pData, { nocrypt: true })
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.drawerData = r.data.returnData;
          })
          .catch((err) => {
            this.$message.error("获取单位失败,请联系管理员");
          });
      },
      //获取套餐项目
      GetClusItemComb(clus_Code) {
        var pData = {
          data: {
            clus_Code: clus_Code,
          },
        };
        ajax
          .post(apiUrls.GetClusItemCombByCode, pData)
          .then((r) => {
            if (!r.data.success) {
              alert(r.data.returnMsg);
              return;
            }
            let data = r.data.returnData;
            // 初始化数据
            this.checkedCities = data.clusComb.filter( x=>x.isAddOrNo=='F');
            for (let i = 0; i < data.clusComb.length; i++) {
              this.price += data.clusComb[i].comb_Price;
            }
         

            this.allowAddComb = data.clusComb.filter(x=>x.isAddOrNo=='T');
          })
          .catch((err) => {
            alert("获取项目失败,请稍后重试");
          });
      },
      //获取所有项目信息
      GetAllClusItemComb() {
        var that = this;
        that.loading = false;
        ajax
          .post(apiUrls.GetAddClusItemList, {})
          .then((r) => {
            if (!r.data.success) {
              alert(r.data.returnMsg);
              return;
            }
            // 初始化数据
            that.CombAllData = JSON.parse(r.data.returnData);
            that.CombData = that.CombAllData;
          })
          .catch((err) => {
            alert("获取项目失败,请稍后重试");
          });
      },
      handleCombClick(tab, event) {},
      isViewPackage(checked) {
        var checkedCities = this.checkedCities;
        const i = checkedCities.indexOf(checked);
        if (checked.isView == "T") {
          checkedCities[i].isView = "F";
        } else {
          checkedCities[i].isView = "T";
        }
        this.checkedCities = checkedCities;
      },
      addPackage(item) {
        if (this.activeName == "second") {
          if (!this.allowAddComb.find((obj) => obj.comb_Code == item.comb_Code)) {
            this.allowAddComb.push(item);
          } else {
            const index = this.allowAddComb.indexOf(item);
            this.allowAddComb.splice(index, 1);
          }
        } else {
          // console.log(!item.BD);
  
          if (
            !this.checkedCities.find((obj) => obj.comb_Code == item.comb_Code)
          ) {
            if (item.BD) {
              let screenCombData = [];
              this.CombAllData.forEach((items) => {
                items.children.filter((ev) => {
                  if (
                    item.BD.includes(ev.comb_Code) &&
                    !this.checkedCities.find(
                      (obj) => obj.comb_Code == ev.comb_Code
                    )
                  ) {
                    screenCombData.push(ev);
                  }
                });
              });
              // if (screenCombData.length > 0) {
              //   this.msgData.selectCombName = item;
              //   this.msgData.ComDataList = screenCombData;
              //   //TODO: this.dialogVisible = true;
              //   return;
              // }
            }
            if (item.ME) {
              let screenCombData = [];
              this.CombAllData.forEach((items) => {
                items.children.filter((ev) => {
                  if (
                    item.ME.includes(ev.comb_Code) &&
                    this.checkedCities.find(
                      (obj) => obj.comb_Code == ev.comb_Code
                    )
                  ) {
                    screenCombData.push(ev);
                  }
                });
              });
              if (screenCombData.length > 0) {
                this.msgData.selectCombName = item;
                this.msgData.ComDataList = screenCombData;
                this.MEdialogVisible = true;
                return;
              }
            }
            item.isView = "T";
            this.price += item.comb_Price;
            if (!this.ClusList.lockPrice) {
              this.ClusList.price = this.price.toFixed(2);
            }
            this.checkedCities.push(item);
          } else {
            if (item.BDComb) {
              let screenCombData = [];
              this.CombAllData.forEach((items) => {
                items.children.filter((ev) => {
                  if (
                    item.BDComb.includes(ev.comb_Code) &&
                    this.checkedCities.find(
                      (obj) => obj.comb_Code == ev.comb_Code
                    )
                  ) {
                    screenCombData.push(ev);
                  }
                });
              });
              if (screenCombData.length > 0) {
                this.msgData.selectCombName = item;
                this.msgData.ComDataList = screenCombData;
                this.BDdialogVisible = true;
                return;
              }
            }
            // const index = this.checkedCities.indexOf(item);
            // this.price -= item.comb_Price;
            this.checkedCities = this.checkedCities.filter(
              (obj) => obj.comb_Code !== item.comb_Code
            );
            this.price -= item.comb_Price;
            if (!this.ClusList.lockPrice) {
              this.ClusList.price = this.price.toFixed(2);
            }
            // this.checkedCities.splice(index, 1);
          }
        }
      },
      selectBDCom() {
        this.msgData.selectCombName.isView = "T";
        this.checkedCities.push(this.msgData.selectCombName);
        this.price += this.msgData.selectCombName.comb_Price;
        this.msgData.ComDataList.forEach((item) => {
          if (!this.checkedCities.includes(item.comb_Code)) {
            item.isView = "T";
            this.checkedCities.push(item);
            this.price += item.comb_Price;
          }
        });
        if (!this.ClusList.lockPrice) {
          this.ClusList.price = this.price.toFixed(2);
        }
        this.dialogVisible = false;
      },
      screenCombData() {
        if (!this.CombName) {
          this.CombData = this.CombAllData;
          return;
        }
        let screenCombData = [];
        this.CombAllData.forEach((item) => {
          item.children.filter((ev) => {
            if (!this.CombName || ev.comb_Name.includes(this.CombName)) {
              screenCombData.push(ev);
            }
            // return (!this.CombName || ev.comb_Name.includes(this.CombName));
          });
        });
        let screenData = [
          {
            text: "筛选结果",
            children: screenCombData,
          },
        ];
        this.CombData = screenData;
      },
      checkedCombData() {
        this.checkedCities = this.checkedAllCities.filter((item) => {
          //筛选
          return (
            !this.checkedCombName || item.comb_Name.includes(this.checkedCombName)
          );
        });
      },
      switchHaoyuanClass(item) {
        let checkedCities = this.checkedCities;
        let allowAddComb = this.allowAddComb;
        for (let i = 0; i < checkedCities.length; i++) {
          if (checkedCities[i].comb_Code == item.comb_Code) {
            return "haoyuanCCC";
          }
        }
        for (let z = 0; z < allowAddComb.length; z++) {
          if (allowAddComb[z].comb_Code == item.comb_Code) {
            return "allowAddCombCCC";
          }
        }
      },
    },
  };
  </script>
    
    <style scoped>
  .clusDiv {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 96%;
    margin: 20px auto;
  }
  .clusDiv .clusTop {
    width: 100%;
    margin-top: 10px;
    display: flex;
  }
  .clusMid {
    margin-top: 20px;
    width: 100%;
  }
  .clusMid .pageNation {
    margin-top: 10px;
  }
  .pageNation .el-pagination {
    display: flex;
    justify-content: flex-end;
  }
  .haoyuan-red {
    color: red;
  }
  .haoyuan-bg {
    background-color: baga(250, 250, 250, 0.3);
    color: #ccc;
    cursor: default !important;
  }
  .main_item {
    widows: 100%;
    height: 4.18rem;
    display: flex;
    align-items: center;
    padding: 0.4rem 0.2rem;
    background-color: #ffffff;
    font-size: 15px;
    box-shadow: 5px 5px 3px #909399;
    margin-bottom: 0.3rem;
  }
  
  .el-footer {
    background-color: #b3c0d1;
    color: #333;
    text-align: center;
    line-height: 60px;
  }
  .el-header {
    width: 100%;
    height: auto !important;
    background-color: #b3c0d1;
  }
  .container-header {
    width: 100%;
  }
  .container-header .container-header-form {
    width: 82%;
    display: inline-table;
  }
  /* .container-header .divRight {
    height: 100px;
    display: flex;
    align-items: center;
  } */
  .container-header .el-form-item {
    margin-bottom: 0.3rem;
  }
  
  .CombHeader {
    width: 100%;
    height: 80px;
    border-bottom: 1px solid #ccc;
    margin-top: 2.28rem;
    margin-left: 1.28rem;
    display: flex;
    align-items: center;
  }
  .textDivItem {
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-align: left;
    font-size: 18px;
    font-family: "Microsoft YaHei", "微软雅黑", "SimHei", "黑体", "Hei",
      sans-serif;
    padding-left: 15px;
  }
  .CombAll {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 1.18rem;
    margin-left: 2.18rem;
  }
  .CombAllDiv {
    /* width: 220px !important; */
    display: flex !important;
    line-height: 40px !important;
    margin-left: 10px;
    align-items: left !important;
    font-size: 15px !important;
    box-shadow: 5px 5px 3px #909399 !important;
    margin-bottom: 1.18rem !important;
  }
  .CombItem {
    /* width: 220px; */
    height: 40px;
    float: left;
    clear: none;
    margin-right: 10px;
    background-color: #ffffff !important;
    /* flex-wrap: wrap; */
    /* margin-right: 1rem !important; */
  }
  .CombItem:nth-child(6n + 1) {
    clear: left;
  }
  .haoyuanCCC {
    background-color: #909399 !important;
    color: #000000 !important;
  }
  .allowAddCombCCC {
    background-color: #7199e9 !important;
    color: #000000 !important;
  }
  /* .CombItem el-checkbox {
    width: 100%;
    height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } */
  .CombItemStyle {
    /* width: 220px; */
    text-align: left;
    display: flex;
    align-items: center;
  }
  .CombItemStyle .checkeDiv {
    width: 15px;
    height: 15px;
    margin-left: 0.2rem;
    border: 1px solid black;
    display: flex;
    align-items: center;
  }
  .checkeDiv .el-icon-check {
    color: #ffffff;
  }
  .CombItemStyle .CombItemName {
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 0.6rem;
  }
  /* .CombItemStyle .CombItemName:hover {
    min-width: 120px;
    width: auto;
    overflow: visible;  
    white-space: normal;
  } */
  .CombItemStyle .CombItemPrice {
    width: 70px;
    font-size: 15px;
    text-align: center;
  }
  .asideACombItem {
    width: 350px !important;
    height: 80px !important;
    display: flex !important;
    line-height: 80px !important;
    /* align-items: center; */
    align-items: center !important;
    padding: 0.4rem 0.2rem !important;
    background-color: #ffffff !important;
    font-size: 14px !important;
    box-shadow: 5px 5px 3px #909399 !important;
    margin-bottom: 1.18rem !important;
    /* margin-right: 1rem !important; */
    margin-bottom: 0rem !important;
    border-bottom: 1px solid #ccc;
  }
  .asideAll {
    height: 50%;
  }
  .asideSelect {
    margin-top: 1.28rem;
  }
  .asideTitle {
    /* width: 320px !important; */
    display: flex !important;
    /* align-items: center; */
    align-items: center !important;
    padding: 0.4rem 0.2rem !important;
    background-color: #ffffff !important;
    font-size: 14px !important;
    box-shadow: 5px 5px 3px #909399 !important;
    margin-bottom: 1.18rem !important;
    /* margin-right: 1rem !important; */
    margin-bottom: 0rem !important;
    border-bottom: 1px solid #ccc;
    height: 40px !important;
    line-height: 40px !important;
  }
  .asideTitle .asideMaLeft {
    margin-left: 14px;
  }
  .asideAStyle {
    /* width: 350px; */
    display: flex;
    text-align: left;
  }
  .asideAStyle .asideAName {
    width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 14px;
  }
  .asideAStyle .asideAPrice {
    width: 40px;
    text-align: right;
  }
  .asideAStyle .asideDelete {
    width: 15px;
    height: 15px;
    margin-left: 2.8rem;
  }
  .asideAStyle .asideicon-view {
    width: 15px;
    height: 15px;
    margin-left: 0.8rem;
  }
  .viewContainer {
    width: 15px;
    height: 15px;
  }
  
  .el-icon-view {
    position: relative;
    z-index: 2;
  }
  .asideicon-onView {
    width: 15px;
    height: 15px;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(
      to bottom right,
      transparent 0%,
      transparent calc(50% - 1px),
      #000000 50%,
      transparent calc(50% + 1px),
      transparent 100%
    );
  }
  .el-textarea {
    width: 520px;
  }
  .el-form-item__label {
    margin-top: 0px !important;
  }
  .combNameDialog {
    border-radius: 5px;
    margin: 5px auto;
    color: red;
    margin-left: 10px;
  }
  </style>
    