<template>
  <div>
    <div class="TjBox">
      <!-- 单位日期 -->
      <div class="company" id="company">
        <!--单位选择 -->
        <div class="companySwitch">
          <el-button @click="drawer = true" type="primary" size="medium">
            切换单位
          </el-button>
          <span>单位：<strong class="companylnc_Name">{{ lnc_Name }}</strong></span>
          <span class="contactPersonSpan" @click="contactPersonClick">(单位联系人)</span>
          <input type="hidden" v-model="lnc_Code" />
          <!--抽屉 -->
          <el-drawer title="单位号源" :visible.sync="drawer" :with-header="false">
            <div class="drawerAll">
              <div class="drawerTop">单位列表</div>
              <div class="drawerSearch">
                <!-- <div class="searchTwo">体检单位预约列表</div> -->
                <div class="searchTwo">
                  <el-input placeholder="单位编码/单位名称" prefix-icon="el-icon-search" v-model="drawerIpnut"
                    size="mini"></el-input>
                  <el-button type="primary" plain size="mini" @click="querys">查询</el-button>
                </div>
              </div>
              <div class="drawerList">
                <div class="drawerTr1">
                  <div class="drawerTd1">
                    <span>单位编码</span>
                  </div>
                  <div class="drawerTd2">
                    <span>单位名称</span>
                  </div>
                </div>
                <div v-for="(item, index) in drawerData" :key="index" class="drawerTr2"
                  :class="{ hoverIndex: index == hoverIndex }" @mouseover="hoverIndex = index" @mouseout="hoverIndex = -1"
                  @click="drawerBtn(index)">
                  <div class="drawerTd3">
                    <!-- <span>{{ item.drawerCode }}</span> -->
                    <span>{{ item.lnc_Code }}</span>
                  </div>
                  <div class="drawerTd4">
                    <!-- <span>{{ item.drawerName }}</span>-->
                    <span>{{ item.lnc_Name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-drawer>
        </div>
      </div>
    </div>
    <div class="lncDiv">
      <div class="lncTop table_top">
        <div>
          <el-input placeholder="客户名称(为空时默认加载全部)" style="width: 240px" v-model="UserName" size="small"></el-input>
          <el-select v-model="selectState" multiple placeholder="状态" size="small" style="margin-left: 10px">
            <el-option v-for="item in SelectOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-date-picker v-model="yearTime" style="width: 240px;margin-left: 10px" type="daterange" align="right" unlink-panels range-separator="-"
              start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy/MM/dd" :picker-options="pickerOptions"
              size="small" @change="handlebeginTimeChange"></el-date-picker> -->
          <el-button type="primary" icon="el-icon-search" @click="GetTeamList" style="margin-left: 10px"
            size="small">查询</el-button>
          <!-- <el-button
            type="primary"
            icon="el-icon-search"
            @click="GetNewData"
            style="margin-left: 10px"
            size="small"
            >查询</el-button
          > -->
        </div>
        <div class="divRight">
          <!-- <el-button
            type="success"
            @click="SendReminderMessages()"
            icon="el-icon-download"
            size="small"
            >修改有效期</el-button
          > -->
          <el-popover placement="bottom" width="600" trigger="click">
            <div class="popover_atnData_top">
              <!-- <div>
                <span>发卡时间:</span>
                <el-radio v-model="DateRadio" label="1">不限日期</el-radio>
                <el-radio v-model="DateRadio" label="2">自选日期</el-radio>
                <div>
                  <el-date-picker v-model="createTime"  type="daterange" align="right" unlink-panels range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy 年 MM 月 dd 日" :picker-options="pickerOptions"
                  size="mini" @change="handlecreateTimeChange"></el-date-picker>
                </div>
              </div> -->
              <div></div>
              <div style="margin-right: 10px;">
                <el-button @click="GetTeamList" size="small">筛选</el-button>
              </div>
            </div>
            <el-table :data="AtnData" row-key="id" @selection-change="handleAtnDataSelectionChange">
              <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
              <el-table-column width="100" property="name" label="名称"></el-table-column>
              <el-table-column width="120" property="createTime" label="激活日期"></el-table-column>
              <el-table-column width="50" property="number" label="数量"></el-table-column>
              <el-table-column width="300" property="validityDate" label="有效期"></el-table-column>
              <el-table-column width="150" property="batch" label="记录批次"></el-table-column>
              <el-table-column width="150" property="group" label="分组"></el-table-column>
            </el-table>
            <el-button slot="reference" size="small" style="margin-right: 10px;">高级筛选</el-button>
          </el-popover>
          <el-button icon="el-icon-bell" @click="showSMSTemplate()" size="small">发送催约短信</el-button>
          <el-button type="primary" icon="el-icon-date" @click="showupdateValidityPeriod()" size="small">修改有效期</el-button>
          <el-button type="info" icon="el-icon-s-release" @click="RevokeAtnTeamList()" size="small">撤销激活</el-button>
          <el-button type="success" @click="exportExcel()" icon="el-icon-download" size="small">Excel导出</el-button>
          <el-button type="danger" icon="el-icon-delete" @click="DeleteTeamList()" size="small">批量删除</el-button>
        </div>
      </div>
      <div class="lncMid">
        <el-table :data="tableData" v-loading="loading" element-loading-text="拼命加载中" ref="tableData" border stripe
          :fit="true" row-key="id" @selection-change="handleSelectionChangePeople" :height="height">
          <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column prop="lnc_Code" label="单位编码" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="sex" label="性别" align="center"></el-table-column>
          <el-table-column prop="age" label="年龄" align="center"></el-table-column>
          <el-table-column prop="tel" label="手机号码" align="center"></el-table-column>
          <el-table-column prop="matrimony" label="婚姻状态" align="center"></el-table-column>
          <el-table-column prop="idCard" label="身份证号" align="center" width="220"></el-table-column>
          <el-table-column prop="state" label="状态" align="center"></el-table-column>
          <!-- <el-table-column prop="department" label="部门" :filters="depaFilters" :filter-method="depaFilterGroup" align="center"></el-table-column> -->
          <el-table-column align="center">
            <template slot="header" slot-scope="scope">
              <span :class="depaCheckList.length > 0 ? 'table_column_header' : ''">部门</span>
              <el-popover placement="bottom" width="200" trigger="click">
                <el-input placeholder="部门" style="padding: 0px;width: 160px; text-align: center; margin-bottom: 5px;"
                  v-model="depaName" size="small"></el-input>
                <!-- <div v-for="(item,index) in depaFilters" :key="index">{{ item.text }}</div> -->
                <el-checkbox-group v-model="depaCheckList" style="max-height: 60vh;overflow-x: hidden;">
                  <el-checkbox v-for="(item, index) in depaFilters" :key="index" :label="item.text"
                    style="width: 160px;margin-right: 0px;" @change="depaFilterGroup"></el-checkbox><br />
                </el-checkbox-group>
                <i class="el-icon-arrow-down" slot="reference"></i>
              </el-popover>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.row.department }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header" slot-scope="scope">
              <span :class="groupCheckList.length > 0 ? 'table_column_header' : ''">分组</span>
              <el-popover placement="bottom" width="200" trigger="click">
                <el-input placeholder="分组" style="padding: 0px;width: 160px; text-align: center; margin-bottom: 5px;"
                  v-model="groupName" size="small"></el-input>
                <el-checkbox-group v-model="groupCheckList" style="max-height: 60vh;overflow-x: auto;">
                  <el-checkbox v-for="(item, index) in filters" :key="index" :label="item.text"
                    style="width: 160px;margin-right: 0px;" @change="depaFilterGroup"></el-checkbox><br />
                </el-checkbox-group>
                <i class="el-icon-arrow-down" slot="reference"></i>
              </el-popover>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.row.group }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="position" label="职级" align="center"></el-table-column>
          <!-- <el-table-column prop="group" label="分组" :filters="filters" :filter-method="filterGroup" align="center"></el-table-column> -->
          <el-table-column prop="periodOfValidity" label="有效期" width="220" align="center"></el-table-column>
          <el-table-column prop="createTime" label="添加时间" width="220" align="center"></el-table-column>
          <el-table-column prop="operator" label="操作员" width="220" align="center"></el-table-column>
          <el-table-column label="操作" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button @click="DYTeamList(scope.row)" v-if="scope.row.state == '未使用'" type="text" plain>代预约</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="index"
            :page-sizes="[200,500, 1000, 2000, 3000, 5000, 10000]" :page-size="size"
            layout="total, sizes, prev, pager, next, jumper" :total="tableCopyTableList.length"></el-pagination>
        </div>
        <div class="pageNation"></div>
        <el-dialog title="修改有效期" :visible.sync="ValidityPeriod" width="30%">
          <div style="display: flex">
            <div>
              <el-form :label-position="'right'" :style="{ height: dialogHeight }">
                <el-form-item>
                  <div>
                    <el-checkbox v-model="CYchecked">催约通知</el-checkbox>
                  </div>
                </el-form-item>
                <el-form-item>
                  <span>有效期</span>

                  <div class="block">
                    <el-date-picker v-model="effectiveDate" type="daterange" range-separator="至" start-placeholder="开始日期"
                      end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="pickerHandleChange">
                    </el-date-picker>
                  </div>
                </el-form-item>
                <el-form-item>
                  <div><span>过期提醒</span></div>
                  <div style="width: 160px; display: inline">
                    <el-select v-model="selectedDay" placeholder="请选择" @change="pickerHandleChange">
                      <el-option v-for="day in 15" :key="day" :value="day" :label="day + '天'"></el-option>
                    </el-select>
                  </div>
                  <span>天</span>
                  <el-time-picker v-model="timeValue" value-format="HH:mm:ss" :picker-options="pickerOptions"
                    placeholder="任意时间点" @change="pickerHandleChange">
                  </el-time-picker>
                </el-form-item>
                <el-form-item>
                  <div><span>过期提醒时间</span></div>
                  <el-input v-model="expireTime" placeholder="" size="small" readonly></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <div slot="footer">
            <el-button type="danger" size="small" @click="ValidityPeriod = false">取消</el-button>
            <el-button type="success" size="small" @click="updateValidityPeriod">保存</el-button>
          </div>
        </el-dialog>
        <el-dialog :title="DYYDialogTitle" :visible.sync="DYYdialogVisible" width="80%" top="5vh">
          <div class="DYYDiv" :style="{ height: dialogHeight }">
            <div class="selectTimeDiv">
              <div class="DYConfirmDiv" style="display: flex;"><span>1、选择时间</span>
                <div style="margin-left: 10px;"><el-checkbox v-model="bookChecked" style="width: 16px;height: 16px;"
                    @change="changeBookChecked">占用全局号源</el-checkbox></div>
              </div>
              <div id="all" v-if="bookChecked">
                <div id="calendar">
                  <div class="month">
                    <div class="prevMonth" @click="prevMonth" v-show="pre">
                      上一月
                    </div>
                    <div class="prevMonth_" v-show="pre_"></div>
                    <div class="year-month">
                      <span class="choose-year">{{ currentDateStr }}</span>
                    </div>
                    <div class="nextMonth" @click="nextMonth">下一月</div>
                  </div>
                  <div class="weekdays">
                    <div class="week-item" v-for="item of weekList" :key="item">
                      {{ item }}
                    </div>
                  </div>
                  <div class="calendar-inner">
                    <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                      v-bind:class="[item.disable ? 'disabled' : '']">
                      <div @click="
                        item.Thing === ''
                          ? ''
                          : matchDate(item.ThingName)
                            ? dayClick(item.date, item.ThingName, index)
                            : ''
                        " :class="ClassKey === calendarList[index].value
    ? 'chooseDay'
    : ''
    ">
                        <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.date }}
                        </div>
                        <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.Thing }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="sumtime" v-show="stateShow">
                  <div :class="items.team_Surplus === 0 ? 'timeTwo' : 'timese'" v-for="(items, index) in sumtimeList"
                    :key="index" @click="timeBtn(items, index)">
                    <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'
                      ">
                      {{ items.sumtime_Name }}
                    </span>
                  </div>
                </div>
              </div>
              <div id="all" v-else>
                <div id="calendar">
                  <div class="month">
                    <div class="prevMonth" @click="prevMonth" v-show="pre">
                      上一月
                    </div>
                    <div class="prevMonth_" v-show="pre_"></div>
                    <div class="year-month">
                      <span class="choose-year">{{ currentDateStr }}</span>
                    </div>
                    <div class="nextMonth" @click="nextMonth">下一月</div>
                  </div>
                  <div class="weekdays">
                    <div class="week-item" v-for="item of weekList" :key="item">
                      {{ item }}
                    </div>
                  </div>
                  <div class="calendar-inner">
                    <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                      v-bind:class="[item.disable ? 'disabled' : '']">
                      <div @click="daybookCheckedClick(item.date, item.ThingName, index)"
                        :class="ClassKey === calendarList[index].value ? 'chooseDay' : ''">
                        <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.date }}
                        </div>
                        <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">{{ item.Thing }}
                        </div>
                        <!-- <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.Thing }}
                        </div> -->
                      </div>
                    </div>
                  </div>
                </div>
                <div class="sumtime" v-show="stateShow">
                  <div class="timese" v-for="(items, index) in sumtimeList" :key="index"
                    @click="timeBookCheckedBtn(items, index)">
                    <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'">
                      {{ items.sumtime_Name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="selectTimeDiv">
              <div class="DYConfirmDiv">
                <span>2、选择套餐</span><span>(代预约只能选择单位套餐)</span>
              </div>
              <el-table ref="singleTable" :data="filteredData" height="60%" highlight-current-row
                @current-change="handleCurrentChangeClus" style="width: 100%">
                <el-table-column prop="clus_Code"> </el-table-column>
                <el-table-column prop="clus_Name"> </el-table-column>
                <el-table-column prop="price" align="center">
                  <template slot="header" slot-scope="scope">
                    <el-input v-model="searchClus_Name" size="small" placeholder="输入关键字搜索" />
                  </template>
                </el-table-column>
              </el-table>
              <div class="DYConfirmDiv">
                <span>3、确认订单</span>
                <div>
                  <span>体检人:</span><span class="DYConfirmSpan">{{ teamName }}</span>
                </div>
                <div>
                  <span>体检时间：</span><span class="DYConfirmSpan">{{ ClassKey }}</span>
                </div>
                <div>
                  <span>体检时段：</span><span class="DYConfirmSpan">{{ sumtime_Name }}</span>
                </div>
                <div>
                  <span>体检套餐：</span><span class="DYConfirmSpan">{{
                    CurrentClus.clus_Name
                  }}</span>
                </div>
                <div>
                  <span>订单总价：</span><span class="DYConfirmSpan">{{
                    CurrentClus.price
                  }}</span>
                </div>
              </div>
            </div>
          </div>
          <div slot="footer">
            <el-button type="danger" size="small" @click="DYYdialogVisible = false">取消</el-button>
            <el-button type="success" size="small" @click="DYConfirm">确定</el-button>
          </div>
        </el-dialog>
        <el-dialog title="发送短信" :visible.sync="SMSdialogVisible" width="80%" id="smsSMSdialogVisible" top="5vh">
          <div style="height: auto;min-height: calc( 100vh - 350px);">
            <span>用户数量：</span><span style="font-size: 18px;color: rgb(219, 33, 0);">{{ ids.length }}个</span>
            <div class="textareaDiv">
              <div class="textareaCon">
                <div style="margin-top: 20px; margin-bottom: 20px; height: 10px">
                  <el-form :model="SMSTemplateList">
                    <el-form-item label="模板名称" label-width="80px">
                      <el-select v-model="SMSTemplateName" placeholder="请选择" @change="selectSMSTemplateTask">
                        <el-option v-for="(item, index) in tableSMSConstData" :key="index" :label="item.sms_Name"
                          :value="item.sms_Code">
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <template>
                        <div class="textareaDiv">
                          <div class="textareaCon">
                            <div style="margin-top: 20px; margin-bottom: 20px; height: 10px">
                              <div id="div-template" class="templet-header">
                                <div class="keywords">
                                  <div class="detail" id="detail">
                                    <span>关键字：</span>
                                    <ul id="memberInfo">
                                      <li isFocus="true" v-for="(nounId, index) in SMSnounIds" :key="index"
                                        @click="selectDetail">{{
                                          nounId.name }}</li>
                                    </ul>
                                  </div>
                                </div>

                                <div class="templet-input">
                                  <div><span class="title">模板内容：</span>
                                    <div class="text">
                                      <textarea class="infoText" style="height: 148px;" v-model="SMSContent"
                                        @click="handleTextareaClick" @keyup="handleKeyup" @keydown="handleKeydown"
                                        ref="textarea" :isFocus="true" :rows="5"></textarea>
                                    </div>
                                  </div>
                                  <div><span class="title">模板预览：</span>
                                    <div class="text">
                                      <div class="textareaCon">
                                        <!-- <span>预览：</span> -->
                                        <div class="infoText" v-html="SMSPreview" style="background-color: aliceblue;">
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div></div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-form-item>
                    <!-- <el-form-item label-width="80px">
                      <template>
                        <el-button
                          class="textareaNou"
                          v-for="(nounId, index) in SMSnounIds"
                          :key="index"
                          @click="textareaAdd(nounId)"
                          type="blue"
                          size="small"
                          >{{ nounId.name }}</el-button
                        >
                      </template>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div></div>
                      <div
                        id="SMSContentTextArea"
                        class="infoText"
                        ref="SMSContentTextArea"
                        :contenteditable="true"
                        v-html="SMSContent"
                        @input="btnHandelClick"
                      ></div>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div class="textareaCon">
                        <span>短信预览：</span>
                        <div class="infoText" v-html="SMSPreview"></div>
                      </div>
                    </el-form-item> -->
                  </el-form>
                  <br />
                  <div></div>
                </div>
              </div>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="SMSdialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="SendReminderMessages">确 定</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
    <contactPerson v-if="editCP" :lnc_Code.sync="lnc_Code" ref="contactPerson_ref" />
  </div>
</template>

<script>
import { ajax } from "../../common/ajax";
import apiUrls from "../../config/apiUrls";
import { storage } from "@/common";
import contactPerson from "../TeamExamination/contactPerson.vue";
export default {
  name: "BookTj",
  components: {
    contactPerson
  },
  data() {
    return {
      overallLoading: "",
      drawer: false, //切换号源抽屉
      UserName: "",
      lnc_Name: "所有单位", //号源值
      drawerIpnut: "", //单位编码名称
      hoverIndex: -1, //表示当前hover的是第几个div 初始为 -1 或 null 不能为0 0表示第一个div
      sourceValue: "总号源",
      everyWidth: "width:calc( 100% / 16)",
      lnc_Code: "",
      drawerData: [], //单位列表
      //团检名单模型
      TeamList: {
        id: "",
        name: "",
        birthday: "",
        sex: "",
        age: "",
        tel: "",
        matrimony: "",
        idCard: "",
        remark: "",
        nation: "",
        employeeId: "",
        department: "",
        group: "",
        position: "",
        address: "",
      },
      ids: "", //id集合 用于批量删除或单个删除
      NameList: "",
      height: "calc( 100vh - 250px)",
      dialogHeight: "calc( 100vh - 350px)",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 200, //页码
      dialogVisible: false,
      dialogVisibleOne: false,
      dialogTitle: "", //对话框的标题
      dialogTitleOne: "",
      loading: false,
      waitNumber: 0,
      yearTime: [], //日期控件的值
      editCP: false,
      effectiveDate: "",
      active: 0,
      activeClus: "firstClus",
      tableClusData: [],
      AtnDialogTitle: "",
      AtnDialogVisible: false,
      SelectOptions: [
        {
          value: "未使用",
          label: "未使用",
        },
        {
          value: "已使用",
          label: "已使用",
        },
        {
          value: "已过期",
          label: "已过期",
        },
      ],
      selectState: [],
      ValidityPeriod: false,
      selectedDay: 5,
      timeValue: "09:00:00",
      pickerOptions: {
        start: "00:00",
        end: "23:59",
        step: "00:15",
        format: "HH:mm",
      },
      expireTime: "",
      CYchecked: false,

      expireTime: "",
      stateShow: false,
      ClassKey: "", //点击选择日期
      years: "", //年
      months: "", //月
      pre: false, //上一月
      pre_: true, //上一月代替
      week: 0, //周几
      current: {}, //当前时间
      calendarList: [], //用于遍历显示
      shareDate: new Date(), //享元模式，用来做优化的,
      weekList: ["日", "一", "二", "三", "四", "五", "六"], // 新增
      CardData: [
        {
          CardText: "入职套餐（不限性别）",
        },
      ],
      sumtimeList: [], //时段数据
      TeamSpans: "", //样式判断
      sumtime_Code: "", //时段编码
      sumtime_Name: "", //时段名称
      searchClus_Name: "",
      CurrentClus: {
        price: 0,
        clus_Code: "",
        clus_Name: ""
      },
      teamName: "",
      ValidityPeriod: false,
      DYYdialogVisible: false,
      ClassKey: "", //点击选择日期
      DYYDialogTitle: "代预约",

      SMSdialogVisible: false,
      SMSContent: "",
      SMSParameter: "",
      SMSPreview: "",
      SMSTemplateList: {
        id: "",
        sms_Code: "",
        sms_Name: "",
        sms_Content: "",
        Preview: "",
        nounId: "",
        state: "",
        parameter: "",
        updateTime: "",
      },
      SMSNounList: [],
      SMSnounIds: [],
      userName: "",
      SMSTemplateName: "",
      tableSMSConstData: [], //存放消息模板
      allKeyWords: [],
      keyWordsJson: [],
      lastKeyCode: 0,
      depaCheckList: [],
      depaName: "",
      bookChecked: true,
      groupCheckList: [],
      groupName: "",
      AtnData: [],
      atnIds:"",
    };
  },
  created() {
    // this.setDate();
    this.loadBtn();
    var lncList = JSON.parse(storage.session.get("lncList"));
    if (lncList == null) {
      this.drawer = true;
    } else {
      this.lnc_Code = lncList.lnc_Code;
      this.lnc_Name = lncList.lnc_Name;
      this.GetTeamList();
    }
    this.GetClusList();

  },
  computed: {
    // 显示当前时间
    currentDateStr() {
      let { year, month } = this.current;
      return `${year}-${this.pad(month + 1)}`;
    },
    filteredData() {
      // 根据关键字筛选数据
      return this.tableClusData.filter(data =>
        !this.searchClus_Name ||
        data.clus_Name.toLowerCase().includes(this.searchClus_Name.toLowerCase())
      );
    },
    filters() {
      // 使用 Set 来删除重复项
      let uniqueData = [...new Set(this.tableConstData)];
      let grouped = uniqueData.reduce((result, item) => {
        let key = item.group;
        result[key] = result[key] || [];
        result[key].push(item);
        return result;
      }, {});
      let filterData = [];
      Object.keys(grouped).forEach((key) => {
        let filter = {};
        filter.text = key;
        filter.value = key;
        filterData.push(filter);
        // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
      });
      let grouplist = [];
      grouplist = filterData.filter((item) => {
        return !this.groupName || item.text.includes(this.groupName);
      });
      // this.filters = filterData;
      return grouplist;
      // return filterData;
    },
    depaFilters() {
      // 使用 Set 来删除重复项
      let uniqueData = [...new Set(this.tableConstData)];
      let depas = uniqueData.reduce((result, item) => {
        let key = item.department;
        result[key] = result[key] || [];
        result[key].push(item);
        return result;
      }, {});
      let depaFilterData = [];
      Object.keys(depas).forEach((key) => {
        let filter = {};
        filter.text = key;
        filter.value = key;
        depaFilterData.push(filter);
        // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
      });
      let depalist = [];
      depalist = depaFilterData.filter((item) => {
        return !this.depaName || item.text.includes(this.depaName);
      });
      // this.filters = filterData;
      return depalist;
    },
  },
  methods: {
    //设置日期
    setDate() {
      var date = new Date();
      date.setDate(date.getDate() + 9); //获取近3天
      //js计算今天的日期
      this.yearTime[0] = new Date().toLocaleDateString();
      this.yearTime[1] = date.toLocaleDateString();

    },
    loadBtn() {
      var pData = {
          kw: this.drawerIpnut,
      };
      ajax
        .post(apiUrls.GetGetlncmenu, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            this.$message.error(r.data.returnMsg);
            return;
          }
          this.drawerData = r.data.returnData;
        })
        .catch((err) => {
          this.$message.error("获取单位失败,请联系管理员");
        });
    },

    querys() {
      this.loadBtn();
    },
    // 点击选择单位
    drawerBtn(index) {
      this.lnc_Name = this.drawerData[index].lnc_Name;
      this.lnc_Code = this.drawerData[index].lnc_Code;
      storage.session.set(
        "lncList",
        JSON.stringify(this.drawerData[index])
      );
      this.drawer = false;
      this.GetTeamList();
    },
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
      let NameList = rows.map((row) => row.name);
      this.NameList = NameList.join(",");
    },
    handleSelectionChangeids(rows) {
      this.clusIds = rows.map((row) => row.id);
    },
    //显示模态框
    showAddorEditDialog(row) {
      if (this.lnc_Code == "") {
        this.$message.warning("请输入单位编码");
        return;
      }
      if (row == undefined) {
        this.dialogTitleOne = "添加单个客户>单位名称:" + this.lnc_Name;
      } else {
        this.dialogTitleOne = "查看/编辑客户>单位名称:" + this.lnc_Name;
      }
      this.TeamList.id = row ? row.id : "";
      this.TeamList.name = row ? row.name : "";
      this.TeamList.sex = row ? row.sex : "";

      this.TeamList.age = row ? row.age : "";
      this.TeamList.idCard = row ? row.idCard : "";
      this.TeamList.matrimony = row ? row.matrimony : "";
      this.TeamList.tel = row ? row.tel : "";
      this.TeamList.group = row ? row.group : "";
      this.dialogVisibleOne = true; //成功后关闭对话框
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取信息
    GetTeamList() {
      var that = this;
      that.loading = true;
      var pData = {
        lnc_Code: this.lnc_Code,
        ids:this.atnIds,
      };
      ajax
        .post(apiUrls.GetAllTeamList, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          // 初始化数据
          let returnArray = r.data.returnData.team;
          let data = returnArray.map((val) => {
            switch (val.state) {
              case 0:
                val.state = "未操作";
                break;
              case 1:
                val.state = "未使用";
                break;
              case 2:
                val.state = "异常";
                break;
              case 3:
                val.state = "已使用";
                break;
              case 4:
                val.state = "已过期";
                break;
              default:
                break;
            }
            switch (val.periodOfValidity) {
              case "至":
                val.periodOfValidity = "/";
                break;
              default:
                break;
            }
            return val;
          });
          that.tableConstData = data;
          that.tableCopyTableList = data;
          this.GetRecordList();
          // that.tableData = data;
          this.$refs.tableData.clearSelection();
          // that.tableData = that.paging(that.size, that.index);
          this.depaCheckList = [];
          this.GetNewData();
          that.loading = false;
        })
        .catch((err) => {
          console.log(err);
          alert("获取团体名单失败,请稍后重试");
        });
    },
    //筛选
    // filterGroup(value, row, column) {
    //   if (value.length === 0) {
    //     return true; // 如果没有选中任何筛选选项，则显示所有行
    //   }
    //   return value.includes(row.group); // 检查行的分组是否包含在筛选选项中
    // },
    filterGroup(value, row) {
      return row.group === value;
    },
    depaFilterGroup: function () {
      var that = this;
      // console.log("123456");
      // this.tableData = this.tableCopyTableList;
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        return (!this.UserName || item.name.includes(this.UserName)) && (!this.selectState.length || this.selectState.some((s) => item.state === s)) &&
          (!this.depaCheckList.length || this.depaCheckList.some((depa) => item.department === depa)) && (!this.groupCheckList.length || this.groupCheckList.some((group) => item.group === group));
      });
      // this.tableData = this.tableCopyTableList;
      // console.log("123456");
      that.tableData = that.paging(that.size, that.index);
    },
    SyncLnc() {
      this.loading = true;
      ajax.post(apiUrls.SyncLnc).then((r) => {
        if (r.data.success) {
          this.$message.success(r.data.returnMsg);

          this.GetTeamList();
        }
      });
    },
    //删除客户名单
    DeleteTeamList(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }

      this.$confirm("确定删除此客户信息吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteTeamListById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetTeamList();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        if (this.selectState.length == 0) {
          return !this.UserName || item.name.includes(this.UserName);
        }
        for (let i = 0; i < this.selectState.length; i++) {
          if (this.selectState[i] == item.state) {
            return !this.UserName || item.name.includes(this.UserName);
          }
        }
      });
      this.tableData = this.tableCopyTableList;
    },
    //获取所有套餐信息
    GetClusList() {
      var that = this;
      that.loading = false;
      var pData = {
          lnc_Code: this.lnc_Code,
      }
      ajax
        .post(apiUrls.GetClusList, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // console.log(r.data.returnData);
          // 初始化数据

          let tableCopyTableList = r.data.returnData.map((val) => {
            switch (val.clus_sex) {
              case "1":
                val.clus_sex = "男";
                break;
              case "0":
                val.clus_sex = "女";
                break;
              case "%":
                val.clus_sex = "不限";
                break;
              default:
                break;
            }
            switch (val.clusType) {
              case "01":
                val.clusType = "健康体检";
                break;
              case "02":
                val.clusType = "职业体检";
                break;
              case "03":
                val.clusType = "从业体检";
                break;
              case "04":
                val.clusType = "招工体检";
                break;
              case "05":
                val.clusType = "学生体检";
                break;
              case "06":
                val.clusType = "征兵体检";
                break;
              default:
                break;
            }
            return val;
          });
          that.tableAllClusData = tableCopyTableList;
          // this.getGroup();
          // this.screenClusData(this.lnc_Code);
        })
        .catch((err) => {
          alert("获取套餐失败,请稍后重试");
        });
    },
    screenClusData(val) {
      var that = this;
      var pData = {
        sex: "3",
        clusIds: val,
      }
      ajax.post(apiUrls.GetTeamClusItemList, pData, { nocrypt: true }).then(r => {
        var data = r.data.returnData;
        that.tableClusData = data;
        // that.provp = false;
      })
      // let clusIds = val.split(",");;
      // that.tableClusData = that.tableAllClusData.filter((item) => {
      //   for (let i = 0; i < clusIds.length; i++) {
      //    if (clusIds[i]==item.id) {
      //     return item;
      //    }
      //   }
      //筛选
      // return val.includes(item.id);
      // });
    },
    // //获取分类
    // getGroup() {
    //   // 使用 Set 来删除重复项
    //   let uniqueData = [...new Set(this.tableData)];
    //   let grouped = uniqueData.reduce((result, item) => {
    //     let key = item.group;
    //     result[key] = result[key] || [];
    //     result[key].push(item);
    //     return result;
    //   }, {});
    //   let filterData = [];
    //   Object.keys(grouped).forEach((key) => {
    //     let filter = {};
    //     filter.text = key;
    //     filter.value = key;
    //     filterData.push(filter);
    //     // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
    //   });
    //   let depas = uniqueData.reduce((result, item) => {
    //     let key = item.department;
    //     result[key] = result[key] || [];
    //     result[key].push(item);
    //     return result;
    //   }, {});
    //   let depaFilterData = [];
    //   Object.keys(depas).forEach((key) => {
    //     let filter = {};
    //     filter.text = key;
    //     filter.value = key;
    //     depaFilterData.push(filter);
    //     // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
    //   });
    //   this.filters = filterData;
    //   console.log(this.filters);
    //   this.depaFilters = depaFilterData;
    // },
    //导出表格
    exportExcel() {
      require.ensure([], () => {
        const { export_json_to_excel } = require("../../common/Export2Excel"); //js存放的位置
        let excelName = this.lnc_Name + "客户名单";
        const tHeader = [
          "姓名",
          "性别",
          "年龄",
          "身份证",
          "电话号码",
          "部门",
          "分组",
          "婚姻状态",
          "状态",
          "有效期",
          "添加时间",
        ]; //生成Excel表格的头部标题栏
        const filterVal = [
          "name",
          "sex",
          "age",
          "idCard",
          "tel",
          "department",
          "group",
          "matrimony",
          "state",
          "periodOfValidity",
          "createTime",
        ]; //生成Excel表格的内容栏（根据自己的数据内容属性填写）
        const list = this.tableData; //需要导出Excel的数据
        const data = this.formatJson(filterVal, list);
        export_json_to_excel(tHeader, data, excelName); //这里可以定义你的Excel表的默认名称
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    pickerHandleChange() {
      let date = new Date(this.effectiveDate[1]);
      date.setDate(date.getDate() - this.selectedDay);
      var year = date.getFullYear();
      var month = (date.getMonth() + 1).toString().padStart(2, "0");
      var day = date.getDate().toString().padStart(2, "0");

      var formattedDate = `${year}-${month}-${day}`;
      this.expireTime = formattedDate + " " + this.timeValue;
    },
    //代预约
    DYTeamList(item) {
      this.ids = [item.id];
      if (this.ids <= 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      // let CurrentClus = this.CurrentClus;
      this.tableClusData = [];
      this.stateShow = false;
      this.ClassKey = "";
      this.sumtime_Name = "";
      // this.CurrentClus = [];
      // this.handleCurrentChangeClus();
      this.teamName = item.name;
      this.screenClusData(item.clusIds);
      // this.$nextTick(() => {

      // });
      this.init();
      //
      // 保存当前选中的行数据
      this.bookChecked = true;
      this.DYYdialogVisible = true;
      // console.log(this.$refs.singleTable);
      this.$nextTick(() => {
        this.$refs.singleTable.setCurrentRow();
        this.CurrentClus = {
          price: "",
          clus_Code: "",
          clus_Name: ""
        };
      });
    },
    handleCurrentChangeClus(row) {
      this.CurrentClus = row;
    },
    changeBookChecked() {
      this.ClassKey = "";
      this.sumtime_Name = "";
      // this.CurrentClus = [];
      // this.handleCurrentChangeClus();
      // this.teamName = "";
      this.TeamSpans = "";
      this.stateShow = false;
    },
    //检查输入的参数
    checkDYConfirmInfo() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (!this.lnc_Name) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (this.ids.length <= 0) {
        this.$message.warning("客户名单为空");
        return false;
      }
      if (!this.ClassKey) {
        this.$message.warning("请选择体检时间");
        return false;
      }
      if (!this.sumtime_Code || !this.sumtime_Name) {
        this.$message.warning("请选择体检时间段");
        return false;
      }
      if (!this.CurrentClus.clus_Code) {
        this.$message.warning("请选择套餐");
        return false;
      }
      if (!this.CurrentClus.clus_Name) {
        this.$message.warning("请选择套餐");
        return false;
      }
      return true;
    },
    DYConfirm() {
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在预约中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (!this.checkDYConfirmInfo()) {
        this.overallLoading.close();
        return;
      }
      let user = JSON.parse(storage.session.get("user"));
      // console.log(user);
      // return
      let pData = {
        data: {
          ids: this.ids,
          code: this.bookChecked ? "1" : "0",
        },
        OrdData: {
          sumtime_Code: this.sumtime_Code,
          sumtime_Name: this.sumtime_Name,
          clus_Code: this.CurrentClus.clus_Code,
          clus_Name: this.CurrentClus.clus_Name,
          price: this.CurrentClus.price,
          begin_Time: this.ClassKey,
          lnc_Code: this.lnc_Code,
          company_Name: this.lnc_Name,
          Operator: user.admin_Name,
        },
      };
      ajax
        .post(apiUrls.DYYTeaamList, pData)
        .then((r) => {
          this.overallLoading.close();
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success(r.data.returnMsg);
          this.DYYdialogVisible = false; //成功后关闭对话框
          this.GetTeamList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    showupdateValidityPeriod() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.effectiveDate = [];
      this.timeValue = "09:00:00";
      this.selectedDay = 5;
      this.expireTime = "";
      this.ValidityPeriod = true;
    },
    verifyupdateValidityPeriod() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (this.effectiveDate.length != 2) {
        this.$message.warning("未获取有效期");
        return false;
      }
      if (!this.expireTime) {
        this.$message.warning("未获取到过期时间");
        return false;
      }
      return true;
    },
    updateValidityPeriod() {
      var that = this;
      let send = "";
      if (this.CYchecked) {
        send = "1";
      } else {
        send = "2";
      }
      //参数验证
      if (!this.verifyupdateValidityPeriod()) {
        return;
      }
      // that.loading = false;
      var pData = {
        data: {
          ids: this.ids,
          code: send,
        },
        teamList: {
          startDate: this.effectiveDate[0],
          endDate: this.effectiveDate[1],
          expireTime: this.expireTime,
        },
      };
      ajax.post(apiUrls.updateValidityPeriod, pData).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.$message.success(r.data.returnMsg);
        this.GetTeamList();
        this.ValidityPeriod = false;
      });
    },
    RevokeAtnTeamList() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.$confirm("确定撤销客户激活信息信息吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.RevokeAtnTeamList, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success(r.data.returnMsg);
            this.GetTeamList();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //时间段选择判断
    timeBtn(items, index) {
      var that = this;
      if (items.team_Surplus <= 0) {
        alert("该时段已无号源！请选择其他时段");
        return;
      }
      that.TeamSpans = index;
      that.sumtime_Name = items.sumtime_Name;
      that.sumtime_Code = items.sumtime_Code;
    },
    //时间段选择判断
    timeBookCheckedBtn(items, index) {
      var that = this;
      // if (items.team_Surplus <= 0) {
      //   alert("该时段已无号源！请选择其他时段");
      //   return;
      // }
      that.TeamSpans = index;
      that.sumtime_Name = items.sumtime_Name;
      that.sumtime_Code = items.sumtime_Code;
    },
    init() {
      // 初始化当前时间
      this.setCurrent();
      this.calendarCreator();
      // this.switchHaoyuanClass();
    },
    // 判断当前月有多少天
    getDaysByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDate();
    },
    getFirstDayByMonths: function (year, month) {
      return new Date(year, month, 1).getDay();
    },
    getLastDayByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDay();
    },
    // 对小于 10 的数字，前面补 0
    pad: function (str) {
      return str < 10 ? `0${str}` : str;
    },
    // 点击上一月
    prevMonth: function () {
      this.current.month--;

      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 点击下一月
    nextMonth: function () {
      this.current.month++;
      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 格式化时间，与主逻辑无关
    stringify: function (year, month, date) {
      let str = [year, this.pad(month + 1), this.pad(date)].join("-");
      return str;
    },
    // 设置或初始化 current
    setCurrent: function (d = new Date()) {
      let year = d.getFullYear();
      let month = d.getMonth();
      let date = d.getDate();
      this.current = {
        year,
        month,
        date,
      };
    },
    // 修正 current
    correctCurrent: function () {
      let { year, month, date } = this.current;

      let maxDate = this.getDaysByMonth(year, month);
      // 预防其他月跳转到2月，2月最多只有29天，没有30-31
      date = Math.min(maxDate, date);

      let instance = new Date(year, month, date);
      this.setCurrent(instance);
    },
    // 生成日期
    calendarCreator: function () {
      // 一天有多少毫秒
      const oneDayMS = 24 * 60 * 60 * 1000;

      let list = [];
      let { year, month } = this.current;

      // 当前月份第一天是星期几, 0-6
      let firstDay = this.getFirstDayByMonths(year, month);
      // 填充多少天 firstDay-1则为周一开始，
      let prefixDaysLen = firstDay === 0 ? 0 : firstDay;
      // 毫秒数
      let begin = new Date(year, month, 1).getTime() - oneDayMS * prefixDaysLen;

      // 当前月份最后一天是星期几, 0-6
      let lastDay = this.getLastDayByMonth(year, month);
      // 填充多少天， 和星期的排放顺序有关
      let suffixDaysLen = lastDay === 0 ? 0 : 6 - lastDay;
      // 毫秒数
      let end =
        new Date(year, month + 1, 0).getTime() + oneDayMS * suffixDaysLen;

      while (begin <= end) {
        // 享元模式，避免重复 new Date
        this.shareDate.setTime(begin);
        let year = this.shareDate.getFullYear();
        let curMonth = this.shareDate.getMonth();
        let date = this.shareDate.getDate();
        let week = this.shareDate.getDay(); // 当前周几
        list.push({
          year: year,
          month: curMonth,
          date: date,
          Thing: "待开",
          week: week,
          ThingName: "",
          disable: curMonth !== month,
          value: this.stringify(year, curMonth, date),
        });

        begin += oneDayMS;
      }
      this.calendarList = list;
      this.judgeHaoyuan();
    },

    // 号源显示
    judgeHaoyuan: function () {
      var that = this;
      if (!this.lnc_Code) {
        return;
      }
      // 当前时间
      var nowDate = this.stringify(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate()
      );

      var pData = {
        lnccode: this.lnc_Code,
        start: "0",
        end: "30",
      };
      ajax
        .post(apiUrls.GetTeamSumList, pData, { nocrypt: true })
        .then((r) => {
          var TeamList = r.data.returnData;
          for (var i = 0; i < that.calendarList.length; i++) {
            that.prevMonthIconShow();
            if (nowDate > that.calendarList[i].value) {
              that.calendarList[i].Thing = "";
              continue;
            }
            for (var j = 0; j < TeamList.length; j++) {
              if (that.calendarList[i].value == TeamList[j].team_Date) {
                if (TeamList[j].team_Flag == "T") {
                  that.calendarList[i].Thing = "休假";
                  that.calendarList[i].ThingName = "休假";
                  break;
                }
                var team_Surplus = TeamList[j].team_Surplus;
                if (TeamList[j].team_Sum <= 0) {
                  that.calendarList[i].Thing = "待开";
                  that.calendarList[i].ThingName = "待开";
                  break;
                }
                if (team_Surplus > 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "充足";
                } else if (team_Surplus == 0) {
                  that.calendarList[i].Thing = "约满";
                  that.calendarList[i].ThingName = "约满";
                } else if (team_Surplus <= 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "紧张";
                }
              }
            }
          }
        })
        .catch((e) => {
          alert("服务异常！请稍等");
          return;
        });
    },
    // 根据日期显示添加类名
    switchHaoyuanClass: function (value) {
      switch (value) {
        case "紧张":
          return "haoyuan-green";
          break;
        case "约满":
          return "haoyuan-red";
          break;
        case "休假":
          return "haoyuan-red";
          break;
        case "充足":
          return "haoyuan-adequate";
          break;
        case "":
          return "";
          break;
      }
    },

    // 号源匹配触发点击
    matchDate: function (date) {
      if (date == "充足" || date == "紧张") {
        return true;
      } else if (date === "约满" || "待开" || "") {
        return false;
      }
    },

    // 当前年月之前不能跳转回去
    prevMonthIconShow: function () {
      if (this.current.year == new Date().getFullYear()) {
        if (this.current.month > new Date().getMonth()) {
          this.pre = true;
          this.pre_ = false;
        } else {
          this.pre = false;
          this.pre_ = true;
        }
      } else if (this.current.year > new Date().getFullYear()) {
        this.pre = true;
        this.pre_ = false;
      }
    },
    // 日期点击事件
    dayClick: function (date, key, index) {
      // 判断能点击的日期是否为号源充足和紧张,休假，待开，""都不可点击
      if (key == "充足" || key == "紧张") {
        this.years = this.current.year;
        this.months = this.current.month + 1;
        this.ClassKey =
          this.years +
          "-" +
          (this.months < 10 ? "0" + this.months : this.months) +
          "-" +
          (date < 10 ? "0" + date : date);
        this.week = this.calendarList[index].week;
        var pData = {
          date_Time: this.ClassKey,
          lnccode: this.lnc_Code,
        };
        //不需要号源时段的项目注释掉
        this.TeamSpans = "";
        var that = this;
        ajax
          .post(apiUrls.GetTeamSumTimeList, pData, { nocrypt: true })
          .then((r) => {
            if (r.data.success) {
              that.stateShow = true;
              this.TeamSpans = "";
              var List = r.data.returnData;
              that.sumtimeList = List;
            }
          })
          .catch((e) => {
            alert("系统异常！请联系管理员");
            return;
          });
        //
        return true;
      } else {
        this.ClassKey = false;
        return false;
      }
    },
    daybookCheckedClick: function (date, key, index) {
      // 判断能点击的日期是否为号源充足和紧张,休假，待开，""都不可点击
      if (key != "待开") {
        this.years = this.current.year;
        this.months = this.current.month + 1;
        this.ClassKey = this.years + "-" + this.months + "-" + date;
        this.week = this.calendarList[index].week;
        var pData = {
          date_Time: this.ClassKey,
          lnccode: this.lnc_Code,
        };
        //不需要号源时段的项目注释掉
        this.TeamSpans = "";
        var that = this;
        ajax
          .post(apiUrls.GetTeamSumTimeList, pData, { nocrypt: true })
          .then((r) => {
            if (r.data.success) {
              that.stateShow = true;
              this.TeamSpans = "";
              var List = r.data.returnData;
              that.sumtimeList = List;
            }
          })
          .catch((e) => {
            alert("系统异常！请联系管理员");
            return;
          });
        //
        return true;
      } else {
        this.ClassKey = false;
        this.TeamSpans = "";
        this.sumtime_Name = "";
        this.stateShow = false;
        return false;
      }
    },

    selectSMSTemplateTask(val) {
      const index = this.tableSMSConstData.findIndex(
        (item) => item.sms_Code === val
      );
      // console.log(this.tableSMSConstData);
      this.SMSTemplateTask(this.tableSMSConstData[index], index);
    },

    showSMSTemplate() {
      this.GetAllSMSTemplateList();
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      // let tableData = this.tableData;
      // let userName = [];
      // for (let i = 0; i < tableData.length; i++) {
      //   for (let z = 0; z < idArr.length; z++) {
      //     if (tableData[i].id == idArr[z]) {
      //       userName.push(tableData[i].name);
      //     }
      //   }
      // }
      // this.userName = userName.join("、");
      this.SMSdialogVisible = true;
    },
    SendReminderMessages() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      let confirmMsg =
        "确定给（" + idArr.length + "）个用户发送短信通知吗, 是否继续?";
      this.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
          sMSTemplate: this.SMSTemplateList,
        };
        ajax
          .post(apiUrls.SendSMSMessagesByTeam, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success(r.data.returnMsg);
            this.SMSdialogVisible = false;
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //短信模板
    GetAllSMSTemplateList() {
      var that = this;
      that.loading = false;
      var pData = {
          code: "team",
      };
      ajax
        .post(apiUrls.GetAllSMSTemplate, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          // that.tableSMSConstData = r.data.returnData.st
          //   ? r.data.returnData.st.filter((item) => {
          //       return item.appid ? item.appid.includes("overdue") : false;
          //     })
          //   : [];
          that.tableSMSConstData = r.data.returnData.st ? r.data.returnData.st.filter((item) => {
            return !item.lnc_Code;
          })
            : [];
          that.SMSNounList = r.data.returnData.sn;
          this.selectSMSTemplateTask("1004");
        })
        .catch((err) => {
          alert("获取短信模板失败,请稍后重试");
        });
    },
    SMSTemplateTask(SMSTemplate) {
      this.SMSTemplateList = SMSTemplate;
      this.SMSPreview = "";
      this.SMSParameter = SMSTemplate.parameter;
      var nounId = SMSTemplate.nounId.split(",");
      var SMSnounIds = [];
      for (let i = 0; i < nounId.length; i++) {
        for (let z = 0; z < this.SMSNounList.length; z++) {
          if (nounId[i] == this.SMSNounList[z].code) {
            SMSnounIds.push(this.SMSNounList[z]);
          }
        }
      }
      this.SMSnounIds = SMSnounIds;
      this.SMSPreview = this.formatString(
        SMSTemplate.preview,
        SMSTemplate.parameter
      );
      this.SMSContent = this.formatContent(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
    },
    formatString(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                this.SMSNounList[z].code +
                " style='color: blue;'>" +
                this.SMSNounList[z].smSdefault +
                "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    formatContent(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        // var spanElement = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "【" +
                this.SMSNounList[z].name +
                "】"
              );
              // spanElement.push(this.SMSNounList[z].code +i);
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }


      // this.spanElementList=spanElement.join(",");
      return formatted;
    },
    handleTextareaClick() {
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);
      this.dealFocusExtend(obj, this.cursorIndex);
    },
    handleKeyup(e) {
      //每次在文本域中输入的时候都要获取其光标位置，以便于其他操作
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);

      //由于我们是禁止输入中文中括号的，而中文中括号输入左右情况不同，需要分别处理
      // console.log(e.keyCode);
      if (e.keyCode == 219) {
        e.preventDefault();
        //这里获取到光标左侧的内容
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);

        //只有输入结束的是右中括号，而且它的前一个字符是左中括号才把它删除，防止把关键字删除掉
        if (/\】/g.test(leftChar) && obj.value.charAt(this.cursorIndex - 2) === '【') {
          obj.value = obj.value.slice(0, this.cursorIndex - 2) + obj.value.slice(this.cursorIndex, obj.value.length);
        }

      } else if (e.keyCode == 221) {
        e.preventDefault();
        //右中括号就好办多了，因为它不会自动带出左中括号
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);
        if (/\】/g.test(leftChar)) {
          obj.value = obj.value.slice(0, this.cursorIndex - 1) + obj.value.slice(this.cursorIndex, obj.value.length);
        }
      }
      //防止上下左右键移动光标进入关键字中
      if ((e.keyCode == 37 || e.keyCode == 39 || e.keyCode == 38 || e.keyCode == 40) && this.lastKeyCode !== 219) {
        dealFocusMove(obj, this.cursorIndex);
      } else if (e.keyCode == 8) {
        //backspace删除的时候删除整个关键字
        // console.log(obj, this.cursorIndex, this.allKeyWords);
        this.dealFocusL(obj, this.cursorIndex, this.allKeyWords);
      } else if (e.keyCode == 46) {
        //delete删除的时候也是删除整个关键字
        this.dealFocusR(obj, this.cursorIndex, this.allKeyWords)
      }
      if (e.keyCode !== 37 && e.keyCode !== 39) {
        //这里防止手动按得左右键影响左中括号判断
        this.lastKeyCode = e.keyCode;
      }
      this.updateSMSPreview()

    },
    handleKeydown(e) {
      if (e.keyCode == 221 || e.keyCode == 219) {
        e.preventDefault();
      }
      if ((e.keyCode == 37 || e.keyCode == 39) && this.this.lastKeyCode === 219) {
        e.preventDefault();
      }
    },

    // initializeKeyWordsJson() {
    //   const newData = this.data1.concat(this.data2).concat(this.data3);
    //   for (let i = 0; i < newData.length; i++) {
    //     if (this.keyWordsJson[newData[i].name] !== null) {
    //       this.keyWordsJson[newData[i].name] = newData[i].id;
    //     }
    //   }
    // },
    // getFocus() {
    //   return this.$refs.textarea.selectionStart;
    // },
    //处理删除关键字
    dealFocusL(obj, index, allKeyWords) {
      var text = obj.value.slice(0, index);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        //获取左中括号位置
        lastIndex = regL.lastIndex;
      }
      while (resR = regR.exec(text)) {
        j++;
      }
      if (i != j) {
        var textAll = obj.value;
        obj.value = textAll.substring(0, lastIndex - 1) + textAll.substring(index, textAll.length);
        this.SMSContent = obj.value;
        // console.log(obj.value);
        // allKeyWords.splice(i - 1, 1);
        obj.setSelectionRange(lastIndex - 1, lastIndex - 1);
        // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }

    },
    //delete关键字
    dealFocusR(obj, index, allKeyWords) {
      var text = obj.value.slice(index, obj.value.length);
      text = text.split('').reverse().join('');
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
      }
      while (resR = regR.exec(text)) {
        j++;
        lastIndex = regR.lastIndex;
      }
      if (i != j) {
        //获取右中括号位置
        var textAll = obj.value;
        lastIndex = index + text.length - lastIndex + 1;
        allKeyWords.splice(j - 1, 1);
        obj.value = textAll.substring(0, index) + textAll.substring(lastIndex, textAll.length);
        this.SMSContent = obj.value;
        obj.setSelectionRange(index, index);
        // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }
    },
    //处理光标上下左右移动
    dealFocusMove(obj, index) {
      var text = obj.value.slice(0, index);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var _lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        lastIndex = regL.lastIndex;
      }
      while (resR = regR.exec(text)) {
        j++;
      }
      if (i != j) {
        if (index == lastIndex) {
          var rightText = regR.exec(obj.value.slice(index, obj.value.length));
          _lastIndex = rightText['index'];
          index = _lastIndex + index + 1;
        } else {
          index = lastIndex - 1;
        }
        obj.selectionStart = index;
        obj.selectionEnd = index;
      }
    },
    //处理鼠标定位光标
    dealFocusExtend(obj, index) {
      var text = obj.value.slice(index, obj.value.length);
      // var text = obj.value;
      // console.log(text);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var firstRightBracketIndex = -1; // 记录第一个右中括号的索引
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;

      }
      while (resR = regR.exec(text)) {
        j++;
        if (firstRightBracketIndex === -1) {
          firstRightBracketIndex = resR.index + index + 1; // 记录第一个右中括号的索引
        }
        lastIndex = regR.index;
      }
      // console.log(index);
      if (i != j) {
        // var text = obj.value;
        index = obj.value.length;
        obj.selectionStart = firstRightBracketIndex;
        obj.selectionEnd = firstRightBracketIndex;
      }
    },
    //获取光标位置
    getFocus(elem) {
      var index = 0;
      if (document.selection) { // IE Support
        elem.focus();
        var Sel = document.selection.createRange();
        if (elem.nodeName === 'TEXTAREA') { //textarea
          var Sel2 = Sel.duplicate();
          Sel2.moveToElementText(elem);
          var index = -1;
          while (Sel2.inRange(Sel)) {
            Sel2.moveStart('character');
            index++;
          };
        } else if (elem.nodeName === 'INPUT') { // input
          Sel.moveStart('character', -elem.value.length);
          index = Sel.text.length;
        }
      } else if (elem.selectionStart || elem.selectionStart == '0') { // Firefox support
        index = elem.selectionStart;
      }
      return (index);
    },
    selectDetail(e) {
      console.log(e);
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);
      //首先判断是否有光标，这样我们的光标位置是不存在的
      if (this.cursorIndex !== null) {
        //这里判断是否是我们要点击的是不是关键字
        if (e.target.tagName !== "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //要添加东西当然要先放入光标了，这里会记住之前的光标位置，所以直接focus即可
          obj.focus();
          this.cursorIndex = this.getFocus(obj);
          var text = obj.value;
          //文本中关键字以中括号包裹的形式显示
          var textNode = text.substring(0, this.cursorIndex) + '【' + e.target.innerHTML + '】' + text.substring(this.cursorIndex, text.length);
          this.allKeyWords.push(e.target.innerHTML);
          obj.value = textNode;
          //添加完之后我们要刷新光标位置
          this.SMSContent = obj.value;
          this.updateSMSPreview();
          this.cursorIndex = this.cursorIndex + e.target.innerHTML.length + 2;
          obj.selectionStart = this.cursorIndex;
          obj.selectionEnd = this.cursorIndex;

        } else if (e.target.tagName == "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //点击文本区域操作
          this.cursorIndex = getFocus(obj);
        } else {
          //点击其他地方要将光标位置置空，防止点击关键字添加
          this.cursorIndex = null;
          console.log(1);
        }
      }
    },
    updateSMSPreview() {
      var obj = document.querySelector('textarea');
      // var templatetypename = document.querySelector('[data-type="templateName"]').value||"";
      //模板原始内容
      var templatename = obj.value || "";
      var regex = /\【([^\【\】]+)\】/g;
      let placeholders = [];
      this.SMSTemplateList.preview = templatename.replace(regex, (match) => {
        placeholders.push(`{${placeholders.length}}`);
        return placeholders[placeholders.length - 1];
      });
      this.SMSTemplateList.parameter = this.getParameter(templatename);
      // console.log(placeholders);
      // console.log(this.SMSTemplateList.parameter);
      console.log(this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter);
      this.SMSPreview = this.formatPreviewString(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
      // console.log(this.SMSPreview);
    },
    formatPreviewString(formatted, parameter) {
      // console.log(formatted, parameter);
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                this.SMSNounList[z].code +
                " style='color: blue;'>" +
                this.SMSNounList[z].smSdefault +
                "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    getParameter(message) {
      // console.log(message);
      // 使用正则表达式提取<span>标签内容
      var regEx = /\【([^\【\】]+)\】/g;
      var spanContents = [];
      var match;

      while ((match = regEx.exec(message))) {
        // 获取匹配到的<span>标签内容
        var spanContent = match[1];
        spanContents.push(spanContent);
      }
      // console.log(spanContents);
      // console.log(spanContents,this.SMSnounIds);
      let nounIds = [];
      for (var i = 0; i < spanContents.length; i++) {
        for (let j = 0; j < this.SMSnounIds.length; j++) {
          // console.log(this.allKeyWords[i]);
          if (spanContents[i] == this.SMSnounIds[j].name) {
            nounIds.push(this.SMSnounIds[j].code);
          }
        }
        // keywords.push(this.keyWordsJson[this.allKeyWords[i]]);
      }
      // console.log(nounIds);
      return nounIds.join(",");
    },
    GetRecordList() {
      var that = this;
      that.loading = true;
      var pData = {
        lnc_Code: this.lnc_Code,
      };
      ajax
        .post(apiUrls.GetActivateRecordList, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          // 初始化数据
          // let returnArray = r.data.returnData.team;
          that.AtnData = r.data.returnData;
          that.loading = false;
        })
        .catch((err) => {
          alert("获取激活记录失败,请稍后重试");
        });
    },
    handleAtnDataSelectionChange(rows){
      this.atnIds = rows.map((row) => row.id);
      console.log(this.atnIds);
    },
    // 查看联系人
    contactPersonClick() {
      this.editCP = true;
      this.$nextTick(() => {
        this.$refs.contactPerson_ref.showAddorEditDialog(JSON.parse(storage.session.get("lncList")));
      });
    },
  },
};
</script>

<style lang="scss">
.TjBox .company {
  height: 40px;
}

/* 表格按钮 */
.lncDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;

  .lncTop {
    width: 100%;
    margin-top: 10px;
    // display: flex;
    // justify-content: space-between;
  }

  .lncMid {
    margin-top: 20px;
    width: 100%;

    .pageNation {
      margin-top: 10px;

      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

.haoyuan-red {
  color: red;
}

.haoyuan-bg {
  background-color: baga(250, 250, 250, 0.3);
  color: #ccc;
  cursor: default !important;
}

.button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .el-button {
    margin-left: 0px;
    margin-top: 5px;
  }
}

.el-upload-list {
  width: 10%;
}

.cell .el-table__column-filter-trigger i {
  font-size: 18px;
}

.container {
  overflow: hidden;
  /* 清除浮动 */
}

.radio-container {
  float: left;
  width: 10%;
  font-size: 27px;
}

.form-container {
  margin-left: 10%;
  width: 90%;
}

.el-radio__inner {
  width: 16px;
  height: 16px;
}

// .el-form-item__label {
//   font-size: 39px; /* 修改小红点的字体大小 */
//   line-height: 16px; /* 修改小红点的行高 */
// }
.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
  font-size: 20px;
  /* 修改小红点的字体大小 */
  line-height: 16px;
  /* 修改小红点的行高 */
}

// .el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--date{
//   width: 20%;
// }
.textareaCon .textareaNou {
  font-size: 12px;
}

.text {
  font-size: 16px;
}

.item {
  padding: 18px 0;
}


.smsDiv,
.textTable {
  margin-top: 10px;
}

.textTable span {
  font-family: Arial;
  font-weight: 700;
}

.textareaDiv {
  width: 100%;
  font-size: 12px !important;
}

.textareaDiv .textareaCon {
  margin-top: 20px;
  width: 100%;
  display: block;
  font-size: 16px;
  margin-left: 0px;
  margin-right: auto;
}

.textareaCon textarea {
  font-size: 16px;
}

.textareaCon .infoText {
  height: 156px;
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  // max-height: 156px;
  // height: 156px;
  // overflow-y: auto; /* 允许内容垂直滚动 */
  // overflow-x: hidden; /* 隐藏水平滚动条 */
  // white-space: nowrap; /* 防止内容自动换行 */
}

.textareaCon .textareaNou {
  // width: 150px;
  // height: 50px;

  font-size: 16px;
  background-color: #dddddd;
  margin-right: 5px;
  margin-top: 5px;
  float: left;
}

#smsSMSdialogVisible .el-dialog__body {
  overflow: auto;
}

.DYConfirmSpan {
  margin-left: 8px;
  color: rgb(224, 103, 22);
}

.DYConfirmDiv div {
  margin-top: 3px;
  font-size: 16px;
}

.table_column_header {
  color: #409EFF;
}

.popover_atnData_top {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgb(212, 212, 212);
}
.popover_atnData_top  .el-radio{
  margin-right: 10px;
}
</style>
