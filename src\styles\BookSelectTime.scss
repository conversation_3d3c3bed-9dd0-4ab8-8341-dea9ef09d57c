.DYYDiv {
    width: 100%;
    display: flex;
    justify-content: space-around;
  }
  
  .DYYDiv .selectTimeDiv {
    // width: 42%;
  }
  
  .btnTime {
    width: 100%;
    display: flex;
  }
  
  
  .timese {
    width: 100%;
    height: 2.5rem;
    background: #ffffff;
    box-shadow: 0 3px 5px 0 rgba(153, 153, 153, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
  }
  
  .TeamSpan {
    width: 100%;
    height: 100%;
    /* text-align: center; */
    display: flex;
    justify-content: center;
    align-items: center;
    background: #018bf0;
    border-radius: 5%;
    color: aliceblue;
  }
  
  //日期
  #all {
    width: 584.33px;
    height: 100%;
    // height: 7.36rem;
    //   position: absolute;
    //   top: 0;
    //   left: 0;
  }
  
  #calendar {
    width: 100%;
    height: 90%;
    //   position: relative;
    //   top: 0;
    //   left: 0;
    background: white;
    /* -moz-box-shadow: 2px 2px 5px #333333;
                -webkit-box-shadow: 2px 2px 5px #333333;
                box-shadow: 2px 2px 5px #333333; */
  }
  
  .month {
    height: 3.88rem;
    line-height: 3.88rem;
    text-align: center;
    font-size: 16px;
    color: #4a4a4a;
    background-color: #fff;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
    letter-spacing: -0.01px;
  }
  
  .prevMonth {
    width: 4.2rem;
    height: 3.88rem;
    line-height: 3.88rem;
    margin-left: 0.4rem;
    color: #6a9be4;
  }
  
  .prevMonth_ {
    width: 4.2rem;
    height: 3.88rem;
    margin-left: 0.4rem;
  }
  
  .nextMonth {
    width: 4.2rem;
    height: 3.8rem;
    line-height: 3.88rem;
    margin-right: 0.4rem;
    color: #6a9be4;
  }
  
  .weekdays {
    height: 2.48rem;
    line-height: 2.48rem;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    color: #9b9b9b;
    font-size: 0.28rem;
    justify-content: space-around;
  }
  
  .weekdays .week-item {
    display: inline-block;
    width: calc(100% / 7);
    text-align: center;
    // font-size: 0.28rem;
    font-size: 16px;
  }
  
  .calendar-inner {
    // margin-left: 1.3rem;
    overflow: hidden;
  }
  
  .calendar-item {
    box-sizing: border-box;
    float: left;
    width: calc(100% / 7);
    height: 4rem;
    text-align: center;
  }
  .calendar-item div{
    width: 100%;
  }
  
  .calendarDate {
    width: 4rem;
    height: 2rem;
    font-size: 16px;
    color: #ccc;
    line-height: 2.65rem;
    text-align: center;
  }
  
  .calendarThing {
    width: 4rem;
    font-size: 16px;
    height: 2rem;
    text-align: center;
    color: #ccc;
  }
  
  .calendar-item.disabled {
    visibility: hidden;
  }
  
  .calendar-item.checked {
    color: red;
  }
  
  .chooseDay {
    // border-radius: 50%;
    background-color: #018bf0;
  }
  
  .chooseDay div {
    width: 100%;
    color: #fff !important;
  }
  
  .haoyuan-red {
    color: #d0021b;
  }
  
  .haoyuan-gray {
    color: #ccc;
  }
  
  .haoyuan-green {
    color: #ff9800;
  }
  
  .haoyuan-adequate {
    color: #1ba05f;
  }
  
  /* 跳转按钮 */
  .teamDiv {
    width: 100%;
    height: 2.68rem;
    position: fixed;
    bottom: 0;
    background: white;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .Teamconfirm {
    width: 92%;
    height: 2.96rem;
    background: #6a9be4;
    border-radius: 5px;
    font-size: 0.32rem;
    color: #ffffff;
    letter-spacing: -0.02px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  /* 消息提示 */
  .text-tip {
    display: block;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 15px 15px;
    line-height: 18px;
    position: fixed;
    left: 50%;
    bottom: 55%;
    -webkit-transform: translate(-50%);
    transform: translate(-50%);
    border-radius: 3px;
    display: none;
    z-index: 9999;
    font-size: 14px;
    text-align: center;
  }
  
  /*时段样式*/
  .sumtime {
    width: 94%;
    height: 2.5rem;
    margin: 0.28rem auto;
    background: #f5f5f5;
    font-size: 1.34rem;
    border-radius: 0.1rem;
    -webkit-box-shadow: 2px 2px 5px #333333;
    box-shadow: 2px 2px 5px #333333;
    display: flex;
    justify-content: space-evenly;
    -webkit-box-align: center;
    align-items: center;
    margin-top: 10px;
  }
  
  .timese {
    width: 44%;
    height: 2.94rem;
    background: #ffffff;
    box-shadow: 0 3px 5px 0 rgba(153, 153, 153, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
  }
  
  .timeTwo {
    width: 44%;
    height: 2.94rem;
    background: #b0acac;
    box-shadow: 0 3px 5px 0 rgba(153, 153, 153, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    color: #d1d0cf;
  }
  
  .TeamSpan {
    width: 100%;
    height: 100%;
    /* text-align: center; */
    display: flex;
    justify-content: center;
    align-items: center;
    background: #018bf0;
    border-radius: 5%;
    color: aliceblue;
  }
  
  .TeamSpanTwo {
    width: 100%;
    height: 100%;
    /* text-align: center; */
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5%;
  }

  #smsSMSdialogVisible .el-dialog__body{
    overflow: auto; 
  }