<template>
  <div :class="sidebarClass">
    <!-- Logo区域 -->
    <div class="sidebar-logo" v-if="showLogo">
      <img
        v-if="logoUrl"
        :src="logoUrl"
        alt="Logo"
        class="logo-img"
      >
      <i v-else class="el-icon-s-home logo-icon"></i>
      <span v-if="!isCollapsed" class="logo-title">{{ title }}</span>
    </div>

    <!-- 菜单区域 -->
    <el-menu
      mode="vertical"
      unique-opened
      :default-active="$route.path"
      background-color="transparent"
      text-color="#606266"
      active-text-color="#1677ff"
      @select="handleSelect"
      :collapse="isCollapsed"
      class="sidebar-menu"
    >
      <sidebar-item :routes="routes" :is-collapsed="isCollapsed"></sidebar-item>
    </el-menu>
  </div>
</template>

<script>
import sidebarItem from "./sidebarItem";
export default {
  name: 'Sidebar',
  data(){
    return{
      indexBreadcrumbs:[],
    }
  },
  components: { sidebarItem },
  watch: {
    $route () {
      this.handChange()
    },
  },
  computed: {
    // 侧边栏折叠状态
    isCollapsed() {
      return this.$store.getters.isCollapse;
    },

    // 侧边栏样式类
    sidebarClass() {
      return this.isCollapsed ? 'sidebar-containers' : 'sidebar-container';
    },

    // 路由数据
    routes() {
      return this.$store.getters.addRoutes;
    },

    // 面包屑数据
    breadcrumbList () {
      let breadcrumbs = []
      let menuList = this.$store.getters.addRoutes;
      this.indexBreadcrumbs.map(item => {
        for (let i = 0; i < menuList.length; i++) {
          if (item === menuList[i].name) {
            breadcrumbs.push(menuList[i])
            if (menuList[i].children) {
              menuList = menuList[i].children
            }
            break;
          }
        }
      })
      return breadcrumbs
    },

    // Logo和标题配置
    showLogo() {
      return true; // 可以根据配置决定是否显示logo
    },

    logoUrl() {
      return ''; // 可以从配置文件或store中获取logo URL
    },

    title() {
      return '管理系统'; // 可以从配置文件中获取标题
    }
  },
 
  methods:{
    // 处理面包屑变化
    handChange () {
      this.$store.commit('promission/breadNow', this.breadcrumbList);
    },

    // 处理菜单选择
    handleSelect(index, indexPath){
      this.indexBreadcrumbs = indexPath;
      console.log('当前选中路径', index, indexPath)
    }
  },

  created () {
    this.handChange();
  }
}
</script>
<style lang="scss" scoped>
.sidebar-container,
.sidebar-containers {
  // Logo区域样式
  .sidebar-logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
    padding: 0 16px;

    .logo-img {
      height: 32px;
      width: auto;
      transition: all 0.3s ease;
    }

    .logo-icon {
      font-size: 24px;
      color: #1677ff;
      transition: all 0.3s ease;
    }

    .logo-title {
      margin-left: 12px;
      font-size: 18px;
      font-weight: 600;
      color: #1677ff;
      transition: all 0.3s ease;
      white-space: nowrap;
    }
  }

  .sidebar-menu {
    height: calc(100vh - 68px);
    overflow-y: auto;
    overflow-x: hidden;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 2px;

      &:hover {
        background: rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// 折叠状态下的特殊样式
.sidebar-containers {
  .sidebar-logo {
    .logo-title {
      opacity: 0;
      width: 0;
      margin-left: 0;
      overflow: hidden;
    }
  }

  .sidebar-menu {
    .el-menu-item,
    .el-submenu__title {
      justify-content: center;

      .svg-icon {
        margin-right: 0;
      }
    }
  }
}
</style>
