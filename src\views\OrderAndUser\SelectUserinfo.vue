<template>
  <div>
    <div class="useDiv">
      <div style="width: 100%">用户查询</div>
      <div class="lncTop">
        <div class="getNew">
          <el-input placeholder="姓名/身份证号/手机号码" v-model="userName" size="small"></el-input>
        </div>

        <el-button type="primary" @click="GetUserInfo" size="small" style="margin-left: 10px">查询</el-button>
      </div>

      <div class="lncMid" id="lncMid">
        <div class="pageNation">用户体检卡</div>
        <div class="lncMid" id="lncMid">
          <el-table :data="tableTeamData" v-loading="loading" element-loading-text="拼命加载中" ref="tableData" border stripe
            :fit="true" row-key="id" @selection-change="handleSelectionChangePeople" :height="height">
            <el-table-column prop="name" label="姓名" align="center"></el-table-column>
            <el-table-column prop="bookingNo" label="卡号" width="180" align="center"></el-table-column>
            <el-table-column prop="sex" label="性别" align="center"></el-table-column>
            <el-table-column prop="age" label="年龄" align="center"></el-table-column>
            <el-table-column prop="tel" label="手机号码" width="180" align="center"></el-table-column>
            <el-table-column prop="matrimony" label="婚姻状态" align="center"></el-table-column>
            <el-table-column prop="idCard" label="证件号" align="center" width="220"></el-table-column>
            <el-table-column prop="lnc_Code" label="单位编码" align="center"></el-table-column>
            <el-table-column prop="lnc_Name" label="单位名称" width="180" align="center"></el-table-column>
            <el-table-column prop="department" label="部门" width="180" align="center"></el-table-column>
            <el-table-column prop="position" label="职级" align="center"></el-table-column>
            <el-table-column prop="state" label="状态" align="center"></el-table-column>
            <el-table-column prop="periodOfValidity" label="有效期" width="220" align="center"></el-table-column>
            <el-table-column prop="createTime" label="添加时间" width="220" align="center"></el-table-column>
            <el-table-column prop="operator" label="操作员" width="220" align="center"></el-table-column>
            <el-table-column label="操作" align="center" fixed="right" width="200">
              <template slot-scope="scope">
                <el-button @click="toLnc(scope.row.lnc_Code)" type="text" size="small" plain>去单位</el-button>
                <el-button @click="DYTeamList(scope.row)" v-if="scope.row.state == '未使用'" type="text"
                  plain>代预约</el-button>
                <el-button type="text" v-if="scope.row.state == '未使用'" @click="RevokeAtnTeamList(scope.row.id)"
                  size="small">撤销激活</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pageNation">订单信息</div>
        <div class="lncMid" id="lncMid">
          <el-table :data="tableOrderData" border stripe :default-sort="{ prop: 'id', order: 'descending' }"
            @selection-change="handleSelectionChangePeople" id="ordertable" :height="height" row-key="id" :fit="true">
            <el-table-column :prop="item.propValue" :label="item.label" :width="item.width" align="center"
              v-for="(item, index) in propData" :key="index"></el-table-column>

            <el-table-column label="操作" width="200" fixed="right" align="center">
              <template slot-scope="scope">
                <el-button @click="updateBeginTime(scope.row)" type="text" v-if="
                  scope.row.state == '待支付' || scope.row.state == '已预约'
                " plain>改期</el-button>
                <el-button @click="SyncOrderByOrderIds(scope.row.id)" type="text" v-if="
                  scope.row.state == '待支付' || scope.row.state == '已预约'
                " plain>导入内网</el-button>
                <el-button @click="SyncOrderByOrderIds(scope.row.id)" type="text" v-if="scope.row.state == '异常订单'"
                  plain>重新导入</el-button>
                <el-button type="text" @click="CancelOrder(scope.row.id)" plain v-if="
                  scope.row.state != '已撤销' && scope.row.state != '已导出'
                ">撤销</el-button>
                <el-button type="text" @click="showViewOrder(scope.row)" plain>查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pageNation"></div>
          <el-drawer size="25%" title="订单详情" :visible.sync="drawerOrder">
            <div class="lncMid" id="lncMid">
              <div class="pageNation">订单信息</div>
              <div class="OrderDe">
                <div class="OrderDein">
                  <span>预约号:</span><span>{{ Order.regno }}</span>
                </div>
                <div class="OrderDein">
                  <span>姓名:</span><span>{{ Order.name }}</span>
                </div>
                <div class="OrderDein">
                  <span>证件号:</span><span>{{ Order.idCard }}</span>
                </div>
                <div class="OrderDein">
                  <span>联系电话:</span><span>{{ Order.tel }}</span>
                </div>
                <div class="OrderDein">
                  <span>套餐名称:</span><span>{{ Order.clus_Name }}</span>
                </div>
                <div class="OrderDein">
                  <span>订单状态:</span><span>{{ Order.state }}</span>
                </div>
                <div class="OrderDein">
                  <span>体检时间:</span><span>{{ Order.begin_Time }}</span>
                </div>
                <div class="OrderDein">
                  <span>预约时间段:</span><span>{{ Order.sumtime_Name }}</span>
                </div>
                <div class="OrderDein">
                  <span>预约时间:</span><span>{{ Order.created_Time }}</span>
                </div>
                <div class="OrderDein" v-if="Order.company_Name">
                  <span>单位名称:</span><span>{{ Order.company_Name }}</span>
                </div>
                <div class="OrderDein">
                  <span>流水号:</span><span>{{ Order.out_trade_no }}</span>
                </div>
                <div class="OrderDein">
                  <span>体检类型:</span><span>{{ Order.type }}</span>
                </div>
                <div class="OrderDein">
                  <span>异常描述:</span><span>{{ Order.errorMsg }}</span>
                </div>
                <!-- 套餐详情 -->
                <div>
                  <el-tabs type="border-card">

                    <div style="display: flex;justify-content: space-between; align-items: center;">
                      <p><span>订单总额：</span>￥{{Order.orderBalance+Order.orderPrice}}</p>
                      <p><span>基础套餐金额：</span>￥{{Order.price}}</p>                 
                      <p><span>订单余额：</span>￥{{Order.orderBalance}}</p>
                   </div>
                    
                    <el-tab-pane label="套餐项目"> 
                     
                      <div class="lncMid" id="lncMid">
                        <el-table :data="clusCodeItems" v-loading="loading" element-loading-text="拼命加载中" :height="height">
                          <el-table-column prop="comb_Name" label="项目名称" align="center"></el-table-column>
                          <el-table-column prop="comb_Price" label="价格" align="center"></el-table-column>
                        </el-table>
                      </div>
                    </el-tab-pane>
                      
              
                    <el-tab-pane label="自选项目">
                      <div class="lncMid" id="lncMid" >
                        <el-table :data="addCombData" v-loading="loading" element-loading-text="拼命加载中" :height="height">
                          <el-table-column prop="comb_Name" label="项目名称" align="center"></el-table-column>
                          <el-table-column prop="comb_Price" label="价格" align="center"></el-table-column>
                        </el-table>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane>
                      <span slot="label"><i class="el-icon-date"></i> 全部项目</span>
                      <div class="lncMid" id="lncMid">
                        <el-table :data="CombData" v-loading="loading" element-loading-text="拼命加载中" :height="height">
                          <el-table-column prop="comb_Name" label="项目名称" align="center"></el-table-column>
                          <el-table-column prop="comb_Price" label="价格" align="center"></el-table-column>
                        </el-table>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
            </div>

          </el-drawer>
          <el-dialog :title="updateTimeDialogTitle" :visible.sync="updateTimedialogVisible" width="40%" top="5vh">
            <div class="DYYDiv" id="updateTimeDialog" :style="{ height: dialogHeight }">
              <div class="selectTimeDiv" style="width: 100%">
                <div class="DYConfirmDiv">
                  <span>选择时间
                    <el-checkbox v-model="bookChecked" style="width: 16px; height: 16px"
                      @change="changeBookChecked">占用全局号源</el-checkbox></span>
                </div>
                <div id="all" style="margin: auto; width: 80%" v-if="bookChecked">
                  <div id="calendar" style="height: 80%">
                    <div class="month">
                      <div class="prevMonth" @click="prevMonth" v-show="pre">
                        上一月
                      </div>
                      <div class="prevMonth_" v-show="pre_"></div>
                      <div class="year-month">
                        <span class="choose-year">{{ currentDateStr }}</span>
                      </div>
                      <div class="nextMonth" @click="nextMonth">下一月</div>
                    </div>
                    <div class="weekdays">
                      <div class="week-item" v-for="item of weekList" :key="item">
                        {{ item }}
                      </div>
                    </div>
                    <div class="calendar-inner">
                      <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                        v-bind:class="[item.disable ? 'disabled' : '']">
                        <div @click="
                          item.Thing === ''
                            ? ''
                            : matchDate(item.ThingName)
                              ? dayClick(item.date, item.ThingName, index)
                              : ''
                          " :class="ClassKey === calendarList[index].value
                            ? 'chooseDay'
                            : ''
                            ">
                          <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.date }}
                          </div>
                          <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.Thing }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="sumtime" v-show="stateShow">
                    <div :class="items.team_Surplus === 0 ? 'timeTwo' : 'timese'" v-for="(items, index) in sumtimeList"
                      :key="index" @click="timeBtn(items, index)">
                      <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'
                        ">
                        {{ items.sumtime_Name }}
                      </span>
                    </div>
                  </div>
                </div>
                <div id="all" style="margin: auto; width: 80%" v-else>
                  <div id="calendar">
                    <div class="month">
                      <div class="prevMonth" @click="prevMonth" v-show="pre">
                        上一月
                      </div>
                      <div class="prevMonth_" v-show="pre_"></div>
                      <div class="year-month">
                        <span class="choose-year">{{ currentDateStr }}</span>
                      </div>
                      <div class="nextMonth" @click="nextMonth">下一月</div>
                    </div>
                    <div class="weekdays">
                      <div class="week-item" v-for="item of weekList" :key="item">
                        {{ item }}
                      </div>
                    </div>
                    <div class="calendar-inner">
                      <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                        v-bind:class="[item.disable ? 'disabled' : '']">
                        <div @click="
                          daybookCheckedClick(
                            item.date,
                            item.ThingName,
                            index
                          )
                          " :class="ClassKey === calendarList[index].value
                            ? 'chooseDay'
                            : ''
                            ">
                          <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.date }}
                          </div>
                          <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.Thing }}
                          </div>
                          <!-- <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.Thing }}
                        </div> -->
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="sumtime" v-show="stateShow">
                    <div class="timese" v-for="(items, index) in sumtimeList" :key="index"
                      @click="timeBookCheckedBtn(items, index)">
                      <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'
                        ">
                        {{ items.sumtime_Name }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div slot="footer">
              <el-button type="danger" size="small" @click="updateTimedialogVisible = false">取消</el-button>
              <el-button type="success" size="small" @click="updateTimeConfirm">确定</el-button>
            </div>
          </el-dialog>
          <el-dialog :title="DYYDialogTitle" :visible.sync="DYYdialogVisible" width="80%" top="5vh">
            <div class="DYYDiv" :style="{ height: dialogHeight }">
              <div class="selectTimeDiv">
                <div class="DYConfirmDiv">
                  <span>1、选择时间</span>
                  <div>
                    <el-checkbox v-model="bookChecked" style="width: 16px; height: 16px"
                      @change="changeBookChecked">占用全局号源</el-checkbox>
                  </div>
                </div>
                <div id="all" v-if="bookChecked">
                  <div id="calendar">
                    <div class="month">
                      <div class="prevMonth" @click="prevMonth" v-show="pre">
                        上一月
                      </div>
                      <div class="prevMonth_" v-show="pre_"></div>
                      <div class="year-month">
                        <span class="choose-year">{{ currentDateStr }}</span>
                      </div>
                      <div class="nextMonth" @click="nextMonth">下一月</div>
                    </div>
                    <div class="weekdays">
                      <div class="week-item" v-for="item of weekList" :key="item">
                        {{ item }}
                      </div>
                    </div>
                    <div class="calendar-inner">
                      <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                        v-bind:class="[item.disable ? 'disabled' : '']">
                        <div @click="
                          item.Thing === ''
                            ? ''
                            : matchDate(item.ThingName)
                              ? dayClick(item.date, item.ThingName, index)
                              : ''
                          " :class="ClassKey === calendarList[index].value
                            ? 'chooseDay'
                            : ''
                            ">
                          <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.date }}
                          </div>
                          <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.Thing }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="sumtime" v-show="stateShow">
                    <div :class="items.team_Surplus === 0 ? 'timeTwo' : 'timese'" v-for="(items, index) in sumtimeList"
                      :key="index" @click="timeBtn(items, index)">
                      <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'
                        ">
                        {{ items.sumtime_Name }}
                      </span>
                    </div>
                  </div>
                </div>
                <div id="all" v-else>
                  <div id="calendar">
                    <div class="month">
                      <div class="prevMonth" @click="prevMonth" v-show="pre">
                        上一月
                      </div>
                      <div class="prevMonth_" v-show="pre_"></div>
                      <div class="year-month">
                        <span class="choose-year">{{ currentDateStr }}</span>
                      </div>
                      <div class="nextMonth" @click="nextMonth">下一月</div>
                    </div>
                    <div class="weekdays">
                      <div class="week-item" v-for="item of weekList" :key="item">
                        {{ item }}
                      </div>
                    </div>
                    <div class="calendar-inner">
                      <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                        v-bind:class="[item.disable ? 'disabled' : '']">
                        <div @click="
                          daybookCheckedClick(
                            item.date,
                            item.ThingName,
                            index
                          )
                          " :class="ClassKey === calendarList[index].value
                            ? 'chooseDay'
                            : ''
                            ">
                          <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.date }}
                          </div>
                          <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                            {{ item.Thing }}
                          </div>
                          <!-- <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.Thing }}
                        </div> -->
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="sumtime" v-show="stateShow">
                    <div class="timese" v-for="(items, index) in sumtimeList" :key="index"
                      @click="timeBookCheckedBtn(items, index)">
                      <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'
                        ">
                        {{ items.sumtime_Name }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="selectTimeDiv">
                <div class="DYConfirmDiv">
                  <span>2、选择套餐</span><span>(代预约只能选择单位套餐)</span>
                </div>
                <el-table ref="singleTable" :data="tableClusData.filter(
                  (data) =>
                    !searchClus_Name ||
                    data.clus_Name
                      .toLowerCase()
                      .includes(searchClus_Name.toLowerCase())
                )
                  " height="60%" highlight-current-row @current-change="handleCurrentChange" style="width: 100%">
                  <el-table-column prop="clus_Code"> </el-table-column>
                  <el-table-column prop="clus_Name"> </el-table-column>
                  <el-table-column prop="price" align="center">
                    <template slot="header" slot-scope="scope">
                      <el-input v-model="searchClus_Name" size="small" placeholder="输入关键字搜索" />
                    </template>
                  </el-table-column>
                </el-table>
                <div class="DYConfirmDiv">
                  <span>3、确认订单</span>
                  <div>
                    <span>体检人:</span><span class="DYConfirmSpan">{{ teamName }}</span>
                  </div>
                  <div>
                    <span>体检时间：</span><span class="DYConfirmSpan">{{ ClassKey }}</span>
                  </div>
                  <div>
                    <span>体检时段：</span><span class="DYConfirmSpan">{{ sumtime_Name }}</span>
                  </div>
                  <div>
                    <span>体检套餐：</span><span class="DYConfirmSpan">{{
                      CurrentClus.clus_Name
                    }}</span>
                  </div>
                  <div>
                    <span>订单总价：</span><span class="DYConfirmSpan">{{ CurrentClus.price }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div slot="footer">
              <el-button type="danger" size="small" @click="DYYdialogVisible = false">取消</el-button>
              <el-button type="success" size="small" @click="DYConfirm">确定</el-button>
            </div>
          </el-dialog>
        </div>
        <div class="pageNation">短信记录</div>
        <!-- @selection-change="handleSelectionChangePeople" row-key="id"-->
        <div class="lncMid">
          <el-table :data="tableData" border stripe :default-sort="{ prop: 'id', order: 'descending' }"
            @selection-change="handleSelectionChangePeople" id="ordertable" :height="height" row-key="id" :fit="true">
            <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>

            <el-table-column :prop="item.propValue" :label="item.label" :width="item.width" align="center" sortable
              v-for="(item, index) in smsPropData" :key="index"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { storage } from "@/common";
import { ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "UserinfoList",
  data() {
    return {
      tableData: [], //表格数据源
      tableOrderData: [], //表格数据源
      tableTeamData: [],
      tableCopyTableList: [], //保存数据 用于分页
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //默认在第一页
      size: 50, //默认一页50条数据
      userName: "", //搜索框的值
      height: "calc( 100vh - 500px)",
      userHeight: "calc( 100vh - 600px)",
      dialogHeight: "calc( 100vh - 350px)",
      // ids: "", //id集合 用于批量删除或单个删除
      propData: [
        //width  有需要就加，其余的自适应
        {
          propValue: "tjCardNo",
          label: "卡号",
          width: "180",
        },
        {
          propValue: "name",
          label: "姓名",
        },

        {
          propValue: "regno",
          label: "预约号",
          width: "180",
        },
        {
          propValue: "idCard",
          label: "身份证",
          width: "180",
        },
        {
          propValue: "tel",
          label: "联系电话",
          width: "180",
        },
        {
          propValue: "type",
          label: "订单类型",
        },
        {
          propValue: "clus_Name",
          label: "套餐名称",
        },
        {
          propValue: "begin_Time",
          label: "体检时间",
          width: "180",
        },
        {
          propValue: "sumtime_Name",
          label: "体检时间段",
          width: "180",
        },
        {
          propValue: "company_Name",
          label: "单位名称",
          width: "220",
        },
        {
          propValue: "department",
          label: "部门",
        },
        {
          propValue: "position",
          label: "职级",
        },
        {
          propValue: "state",
          label: "订单状态",
        },
        {
          propValue: "operator",
          label: "操作员",
        },
        {
          propValue: "created_Time",
          label: "添加时间",
          width: "180",
        },
      ],
      loading: false,
      drawerOrder: false,
      CombAllData: [],
      CombData: [],
      Order: {
        id: "",
        name: "",
        idCard: "",
        tel: "",
        clus_Name: "",
        state: "",
        begin_Time: "",
        created_Time: "",
        regno: "",
        company_Name: "",
        out_trade_no: "",
        sumtime_Name: "",
        type: "",
        errormsg: "",
      },
      overallLoading: "",
      expireTime: "",
      stateShow: false,
      ClassKey: "", //点击选择日期
      years: "", //年
      months: "", //月
      pre: false, //上一月
      pre_: true, //上一月代替
      week: 0, //周几
      current: {}, //当前时间
      calendarList: [], //用于遍历显示
      shareDate: new Date(), //享元模式，用来做优化的,
      weekList: ["日", "一", "二", "三", "四", "五", "六"], // 新增
      CardData: [
        {
          CardText: "入职套餐（不限性别）",
        },
      ],
      sumtimeList: [], //时段数据
      TeamSpans: "", //样式判断
      sumtime_Code: "", //时段编码
      sumtime_Name: "", //时段名称
      searchClus_Name: "",
      CurrentClus: [],
      teamName: "",
      ValidityPeriod: false,
      updateTimedialogVisible: false,
      ClassKey: "", //点击选择日期
      updateTimeDialogTitle: "修改体检时间",
      DYYDialogTitle: "代预约",
      DYYdialogVisible: false,
      orderId: "",
      tableClusData: [],
      addCombData:[],
      clusCodeItems:[],
      smsPropData: [
        //width  有需要就加，其余的自适应
        {
          propValue: "id",
          label: "id",
          width: "60",
        },
        {
          propValue: "tel",
          label: "联系电话",
          width: "180",
        },
        {
          propValue: "msgid",
          label: "msgid",
          width: "180",
        },
        {
          propValue: "content",
          label: "内容",
        },
        {
          propValue: "state",
          label: "状态",
          width: "90",
        },
        {
          propValue: "createTime",
          label: "添加时间",
          width: "180",
        },
      ],
      type: "",
      bookChecked: true,
    };
  },
  created() {
    let userName = this.$route.query.code;
    console.log(userName);
    if (userName) {
      this.userName = userName;
      this.GetUserInfo();
    }
    // this.GetUserInfo();
    this.GetAllClusItemComb();
    this.GetClusList();
  },
  computed: {
    // 显示当前时间
    currentDateStr() {
      let { year, month } = this.current;
      return `${year}-${this.pad(month + 1)}`;
    },
  },
  methods: {
    //跳转单位
    toLnc(toLnc) {
      this.$router.push({
        path: "/TeamExamination/BookCombList",
        query: {
          lnc_Code: toLnc,
        },
      });
    },
    //获取所有个检订单
    GetUserInfo() {
      var that = this;
      if (!this.userName) {
        this.$message.warning("请输入用户信息");
        return;
      }

      var pData = {
        code: this.userName,
      };

      ajax
        .post(apiUrls.GetUserInfo, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          var data = r.data.returnData;
          // 初始化数据
          that.tableConstData = data.user;
          that.tableCopyTableList = data;
          let returnMap = data.order;
          let returnArray = returnMap.map((val) => {
            val.begin_Time = val.begin_Time.substr(0, 10);

            var date = new Date(val.created_Time);
            val.created_Time =
              date.getFullYear() +
              "-" +
              (date.getMonth() + 1) +
              "-" +
              date.getDate() +
              " " +
              date.getHours() +
              ":" +
              date.getMinutes() +
              ":" +
              date.getSeconds();

            switch (val.type) {
              case "person":
                val.type = "个人体检";
                break;
              case "healthcard":
                val.type = "健康证体检";
                break;
              case "staff":
                val.type = "入职体检";
                break;
              case "group":
                val.type = "单位体检";
                break;
              case "nucleicAcid":
                val.type = "核酸体检";
                break;
              default:
                break;
            }

            switch (val.state) {
              case "F":
                val.state = "已预约";
                break;
              case "C":
                val.state = "已撤销";
                break;
              case "export":
                val.state = "已导出";
                break;
              case "E":
                val.state = "异常订单";
                break;
              case "Refund":
                val.state = "已退费";
                break;
              case "T":
                val.state = "已超时";
                break;
              case "S":
                val.state = "待支付";
                break;
              default:
                break;
            }
            return val;
          });
          let returnTeamMap = r.data.returnData.team;

          let returnTeam = returnTeamMap.map((val) => {
            switch (val.state) {
              case 0:
                val.state = "未操作";
                break;
              case 1:
                val.state = "未使用";
                break;
              case 2:
                val.state = "异常";
                break;
              case 3:
                val.state = "已使用";
                break;
              case 4:
                val.state = "已过期";
                break;
              default:
                break;
            }
            switch (val.periodOfValidity) {
              case "至":
                val.periodOfValidity = "/";
                break;
              default:
                break;
            }
            return val;
          });
          that.tableData = data.sms;
          that.tableOrderData = returnArray;
          that.tableTeamData = returnTeam;
        })
        .catch((err) => {
          alert("获取订单失败,请稍后重试");
        });
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    handleCurrentChange(row) {
      this.CurrentClus = row;
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取用户信息
    GetAllUser() {
      var that = this;
      ajax
        .post(apiUrls.GetUserInfoList)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          console.log(r);
          // 初始化数据
          that.tableConstData = r.data.returnData;
          that.tableCopyTableList = r.data.returnData;
          that.tableData = this.paging(this.size, this.index);
        })
        .catch((err) => {
          alert("获取用户失败失败,请稍后重试");
        });
    },
    showViewOrder(row) {
      console.log(row);
      this.CombData = [];
      if (row.choose_comb_code) {
        let CombData = [];
        let combs = row.choose_comb_code.split(",");
        this.CombAllData.forEach((item) => {
          for (let i = 0; i < item.children.length; i++) {
            for (let z = 0; z < combs.length; z++) {
              if (item.children[i].comb_Code == combs[z]) {
                CombData.push(item.children[i]);
              }          
            }
          }
        });
        this.CombData = CombData;
      }
     //加项项目
      if (row.clusOutCode) {
        let addCombData = [];
        let addcombs = row.clusOutCode.split(",");
        this.CombAllData.forEach((item) => {
          for (let i = 0; i < item.children.length; i++) {
            for (let z = 0; z < addcombs.length; z++) {
              if (item.children[i].comb_Code == addcombs[z]) {
                addCombData.push(item.children[i]);
              }          
            }
          }
        });
        this.addCombData = addCombData;
        console.log(this.addCombData);
      }
      if(row.clusCode){
        let clusCodeItems = [];
        let clusCodes = row.clusCode.split(",");
        this.CombAllData.forEach((item) => {
          for (let i = 0; i < item.children.length; i++) {
            for (let z = 0; z < clusCodes.length; z++) {
              if (item.children[i].comb_Code == clusCodes[z]) {
                clusCodeItems.push(item.children[i]);
              }          
            }
          }
        });
        this.clusCodeItems = clusCodeItems;
        console.log(this.clusCodeItems,"项目套餐");
        
      }
      this.Order.id = row ? row.id : "";
      this.Order.name = row ? row.name : "";
      this.Order.idCard = row ? row.idCard : "";
      this.Order.tel = row ? row.tel : "";
      this.Order.clus_Name = row ? row.clus_Name : "";
      this.Order.begin_Time = row ? row.begin_Time : "";
      this.Order.state = row ? row.state : "";
      this.Order.created_Time = row ? row.created_Time : "";
      this.Order.regno = row ? row.regno : "";
      this.Order.company_Name = row ? row.company_Name : "";
      this.Order.out_trade_no = row ? row.out_trade_no : "";
      this.Order.sumtime_Name = row ? row.sumtime_Name : "";
      this.Order.type = row ? row.type : "";
      this.Order.errorMsg = row ? row.errormsg : "";
      this.Order.orderBalance = row ? row.orderBalance : "";
      this.Order.orderPrice= row ? row.orderPrice : "";
      this.Order.price = row ? row.price : "";
      this.drawerOrder = true;

      console.log(this.CombData)
    },
    //保存选中的角色id
    handleSelectionChangePeople(rows) {
      console.log("当前用户管理勾选的值", rows);
      this.ids = rows.map((row) => row.id);
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        return !this.name || item.name.includes(this.name);
      });
      this.tableData = this.paging(this.size, this.index);
    },
    //获取所有项目信息
    GetAllClusItemComb() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAddClusItemList, {})
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          that.CombAllData = JSON.parse(r.data.returnData);
        })
        .catch((err) => {
          alert("获取项目失败,请稍后重试");
        });
    },
    //撤销订单
    CancelOrder(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }

      this.$confirm("确定撤销此订单吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };

        ajax
          .post(apiUrls.CancelGrpOrder, pData)
          .then((r) => {
            if (!r.data.success) {
              alert(r.data.returnMsg);
              return;
            }
            this.$message.success("撤销成功");
            this.GetUserInfo();
          })
          .catch((err) => {
            alert("获取订单失败,请稍后重试");
          });
      });
    },
    //代预约
    DYTeamList(item) {
      // console.log(item);
      this.ids = [item.id];
      if (this.ids <= 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.lnc_Code = item.lnc_Code;
      this.stateShow = false;
      this.ClassKey = "";
      this.sumtime_Name = "";
      this.CurrentClus = [];
      this.type = "group";
      this.screenClusData(item.clusIds);
      this.init();
      // let tableData = this.tableData;
      // let teamName = [];
      // for (let i = 0; i < tableData.length; i++) {
      //   for (let z = 0; z < this.ids.length; z++) {
      //     if (tableData[i].id == this.ids[z]) {
      //       teamName.push(tableData[i].name);
      //     }
      //   }
      // }
      this.teamName = item.name;
      this.bookChecked = true;
      this.DYYdialogVisible = true;
      this.$nextTick(() => {
        this.$refs.singleTable.setCurrentRow();
        this.CurrentClus = {
          price: "",
          clus_Code: "",
          clus_Name: "",
        };
      });
    },
    changeBookChecked() {
      this.ClassKey = "";
      this.sumtime_Name = "";
      // this.CurrentClus = [];
      // this.handleCurrentChangeClus();
      // this.teamName = "";
      this.TeamSpans = "";
      this.stateShow = false;
    },
    //检查输入的参数
    checkDYConfirmInfo() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      // if (!this.lnc_Name) {
      //   this.$message.warning("未获取到单位");
      //   return false;
      // }
      if (this.ids.length <= 0) {
        this.$message.warning("客户名单为空");
        return false;
      }
      if (!this.ClassKey) {
        this.$message.warning("请选择体检时间");
        return false;
      }
      if (!this.sumtime_Code || !this.sumtime_Name) {
        this.$message.warning("请选择体检时间段");
        return false;
      }
      if (!this.CurrentClus.clus_Code) {
        this.$message.warning("请选择套餐");
        return false;
      }
      if (!this.CurrentClus.clus_Name) {
        this.$message.warning("请选择套餐");
        return false;
      }
      return true;
    },
    DYConfirm() {
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在预约中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (!this.checkDYConfirmInfo()) {
        this.overallLoading.close();
        return;
      }
      let user = JSON.parse(storage.session.get("user"));
      // console.log(user);
      // return
      let pData = {
        data: {
          ids: this.ids,
          code: this.bookChecked ? "1" : "0",
        },
        OrdData: {
          sumtime_Code: this.sumtime_Code,
          sumtime_Name: this.sumtime_Name,
          clus_Code: this.CurrentClus.clus_Code,
          clus_Name: this.CurrentClus.clus_Name,
          price: this.CurrentClus.price,
          begin_Time: this.ClassKey,
          lnc_Code: this.lnc_Code,
          company_Name: this.lnc_Name,
          Operator: user.admin_Name,
        },
      };
      ajax
        .post(apiUrls.DYYTeaamList, pData)
        .then((r) => {
          this.overallLoading.close();
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success(r.data.returnMsg);
          this.DYYdialogVisible = false; //成功后关闭对话框
          this.GetUserInfo(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    RevokeAtnTeamList(id) {
      let idArr = [id];
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.$confirm("确定撤销客户激活信息信息吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.RevokeAtnTeamList, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success(r.data.returnMsg);
            this.GetUserInfo();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //改期
    updateBeginTime(item) {
      this.orderId = item.id;
      if (!this.orderId) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.type = "";
      this.lnc_Code = item.lnc_Code;
      this.stateShow = false;
      this.ClassKey = "";
      this.sumtime_Name = "";
      this.CurrentClus = [];
      console.log(item.type);
      switch (item.type) {
        case "个人体检":
          this.type = "person";
          break;
        case "健康证体检":
          this.type = "healthcard";
          break;
        case "入职体检":
          this.type = "staff";
          break;
        case "核酸体检":
          this.type = "nucleicAcid";
          break;
        case "单位体检":
          this.type = "group";
          break;
        default:
          break;
      }
      this.init();
      // let tableData = this.tableData;
      // let teamName = [];
      // for (let i = 0; i < tableData.length; i++) {
      //   for (let z = 0; z < this.ids.length; z++) {
      //     if (tableData[i].id == this.ids[z]) {
      //       teamName.push(tableData[i].name);
      //     }
      //   }
      // }
      this.teamName = item.name;
      this.bookChecked = true;
      this.updateTimedialogVisible = true;
    },
    //检查输入的参数
    checkupdateTimeConfirm() {
      if (this.orderId.length <= 0) {
        this.$message.warning("客户名单为空");
        return false;
      }
      if (!this.ClassKey) {
        this.$message.warning("请选择体检时间");
        return false;
      }
      if (!this.sumtime_Code || !this.sumtime_Name) {
        this.$message.warning("请选择体检时间段");
        return false;
      }
      return true;
    },
    updateTimeConfirm() {
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在修改中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (!this.checkupdateTimeConfirm()) {
        this.overallLoading.close();
        return;
      }
      let pData = {
        data: {
          id: this.orderId,
          code: this.bookChecked ? "1" : "0",
        },
        OrdData: {
          sumtime_Code: this.sumtime_Code,
          sumtime_Name: this.sumtime_Name,
          begin_Time: this.ClassKey,
        },
      };
      ajax
        .post(apiUrls.updateBeginTimeConfirm, pData)
        .then((r) => {
          this.overallLoading.close();
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success(r.data.returnMsg);
          this.updateTimedialogVisible = false; //成功后关闭对话框
          // this.GetTeamList(); //重新加载
          this.GetUserInfo();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //时间段选择判断
    timeBtn(items, index) {
      var that = this;
      if (items.team_Surplus <= 0) {
        Toast("该时段已无号源！请选择其他时段");
        return;
      }
      that.TeamSpans = index;
      that.sumtime_Name = items.sumtime_Name;
      that.sumtime_Code = items.sumtime_Code;
    },
    timeBookCheckedBtn(items, index) {
      var that = this;
      // if (items.team_Surplus <= 0) {
      //   alert("该时段已无号源！请选择其他时段");
      //   return;
      // }
      that.TeamSpans = index;
      that.sumtime_Name = items.sumtime_Name;
      that.sumtime_Code = items.sumtime_Code;
    },
    init() {
      // this.overallLoading = this.$loading({
      //   lock: true,
      //   text: "正在加载中",
      //   spinner: "el-icon-loading",
      //   background: "rgba(0, 0, 0, 0.7)",
      // });
      // 初始化当前时间
      this.setCurrent();
      this.calendarCreator();
      // this.switchHaoyuanClass();
    },
    // 判断当前月有多少天
    getDaysByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDate();
    },
    getFirstDayByMonths: function (year, month) {
      return new Date(year, month, 1).getDay();
    },
    getLastDayByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDay();
    },
    // 对小于 10 的数字，前面补 0
    pad: function (str) {
      return str < 10 ? `0${str}` : str;
    },
    // 点击上一月
    prevMonth: function () {
      this.current.month--;

      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 点击下一月
    nextMonth: function () {
      this.current.month++;
      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 格式化时间，与主逻辑无关
    stringify: function (year, month, date) {
      let str = [year, this.pad(month + 1), this.pad(date)].join("-");
      return str;
    },
    // 设置或初始化 current
    setCurrent: function (d = new Date()) {
      let year = d.getFullYear();
      let month = d.getMonth();
      let date = d.getDate();
      this.current = {
        year,
        month,
        date,
      };
    },
    // 修正 current
    correctCurrent: function () {
      let { year, month, date } = this.current;

      let maxDate = this.getDaysByMonth(year, month);
      // 预防其他月跳转到2月，2月最多只有29天，没有30-31
      date = Math.min(maxDate, date);

      let instance = new Date(year, month, date);
      this.setCurrent(instance);
    },
    // 生成日期
    calendarCreator: function () {
      // 一天有多少毫秒
      const oneDayMS = 24 * 60 * 60 * 1000;

      let list = [];
      let { year, month } = this.current;

      // 当前月份第一天是星期几, 0-6
      let firstDay = this.getFirstDayByMonths(year, month);
      // 填充多少天 firstDay-1则为周一开始，
      let prefixDaysLen = firstDay === 0 ? 0 : firstDay;
      // 毫秒数
      let begin = new Date(year, month, 1).getTime() - oneDayMS * prefixDaysLen;

      // 当前月份最后一天是星期几, 0-6
      let lastDay = this.getLastDayByMonth(year, month);
      // 填充多少天， 和星期的排放顺序有关
      let suffixDaysLen = lastDay === 0 ? 0 : 6 - lastDay;
      // 毫秒数
      let end =
        new Date(year, month + 1, 0).getTime() + oneDayMS * suffixDaysLen;

      while (begin <= end) {
        // 享元模式，避免重复 new Date
        this.shareDate.setTime(begin);
        let year = this.shareDate.getFullYear();
        let curMonth = this.shareDate.getMonth();
        let date = this.shareDate.getDate();
        let week = this.shareDate.getDay(); // 当前周几
        list.push({
          year: year,
          month: curMonth,
          date: date,
          Thing: "待开",
          week: week,
          ThingName: "",
          disable: curMonth !== month,
          value: this.stringify(year, curMonth, date),
        });

        begin += oneDayMS;
      }
      this.calendarList = list;
      if (this.type == "staff") {
        this.judgeHaoyuanPer();
      } else {
        this.judgeHaoyuan();
      }
    },

    // 号源显示
    judgeHaoyuan: function () {
      var that = this;

      if (!this.lnc_Code) {
        // console.log(this.lnc_Code);
        // this.$message.warning("lnc_Code为NULL!请稍后再试");
        // return;
        this.lnc_Code = "0451";
      }
      // 当前时间
      var nowDate = this.stringify(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate()
      );

      var pData = {
        lnccode: this.lnc_Code,
        start: "0",
        end: 60,
      };
      ajax
        .post(apiUrls.GetTeamSumList, pData, { nocrypt: true })
        .then((r) => {
          // this.overallLoading.close();
          var TeamList = r.data.returnData;
          for (var i = 0; i < that.calendarList.length; i++) {
            that.prevMonthIconShow();
            if (nowDate > that.calendarList[i].value) {
              that.calendarList[i].Thing = "";
              continue;
            }
            for (var j = 0; j < TeamList.length; j++) {
              if (that.calendarList[i].value == TeamList[j].team_Date) {
                if (TeamList[j].team_Flag == "T") {
                  that.calendarList[i].Thing = "休假";
                  that.calendarList[i].ThingName = "休假";
                  break;
                }
                var team_Surplus = TeamList[j].team_Surplus;
                if (TeamList[j].team_Sum <= 0) {
                  that.calendarList[i].Thing = "待开";
                  that.calendarList[i].ThingName = "待开";
                  break;
                }
                if (team_Surplus > 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "充足";
                } else if (team_Surplus == 0) {
                  that.calendarList[i].Thing = "约满";
                  that.calendarList[i].ThingName = "约满";
                } else if (team_Surplus <= 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "紧张";
                }
              }
            }
          }
          // console.log(that.calendarList);
        })
        .catch((e) => {
          alert("服务异常！请稍等");
          return;
        });
    },
    judgeHaoyuanPer: function () {
      var that = this;
      var nowDate = this.stringify(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate()
      );
      var pData = {
        type: that.type,
        start: "0",
        end: "30",
      };
      ajax
        .post(apiUrls.GetPersonSumList, pData, { nocrypt: true })
        .then((r) => {
          var personList = r.data.returnData;
          for (var i = 0; i < that.calendarList.length; i++) {
            that.prevMonthIconShow();
            if (nowDate > that.calendarList[i].value) {
              that.calendarList[i].Thing = "";
              continue;
            }
            for (var j = 0; j < personList.length; j++) {
              if (that.calendarList[i].value == personList[j].person_Date) {
                if (personList[j].person_Flag == "T") {
                  that.calendarList[i].Thing = "休假";
                  that.calendarList[i].ThingName = "休假";
                  break;
                }
                var person_Surplus = personList[j].person_Surplus;
                if (personList[j].person_Sum <= 0) {
                  that.calendarList[i].Thing = "待开";
                  that.calendarList[i].ThingName = "待开";
                  break;
                }
                if (person_Surplus > 10) {
                  that.calendarList[i].Thing = "余" + person_Surplus + "人";
                  that.calendarList[i].ThingName = "充足";
                } else if (person_Surplus == 0) {
                  that.calendarList[i].Thing = "约满";
                  that.calendarList[i].ThingName = "约满";
                } else if (person_Surplus <= 10) {
                  that.calendarList[i].Thing = "余" + person_Surplus + "人";
                  that.calendarList[i].ThingName = "紧张";
                }
              }
            }
          }
        })
        .catch((e) => {
          alert("服务异常！请稍等");
          return;
        });
    },
    // 根据日期显示添加类名
    switchHaoyuanClass: function (value) {
      switch (value) {
        case "紧张":
          return "haoyuan-green";
          break;
        case "约满":
          return "haoyuan-red";
          break;
        case "休假":
          return "haoyuan-red";
          break;
        case "充足":
          return "haoyuan-adequate";
          break;
        case "":
          return "";
          break;
      }
    },

    // 号源匹配触发点击
    matchDate: function (date) {
      if (date == "充足" || date == "紧张") {
        return true;
      } else if (date === "约满" || "待开" || "") {
        return false;
      }
    },

    // 当前年月之前不能跳转回去
    prevMonthIconShow: function () {
      if (this.current.year == new Date().getFullYear()) {
        if (this.current.month > new Date().getMonth()) {
          this.pre = true;
          this.pre_ = false;
        } else {
          this.pre = false;
          this.pre_ = true;
        }
      } else if (this.current.year > new Date().getFullYear()) {
        this.pre = true;
        this.pre_ = false;
      }
    },
    // 日期点击事件
    dayClick: function (date, key, index) {
      if (this.type == "group" || this.type == "person") {
        // 判断能点击的日期是否为号源充足和紧张,休假，待开，""都不可点击
        if (key == "充足" || key == "紧张") {
          this.years = this.current.year;
          this.months = this.current.month + 1;
          this.ClassKey =
            this.years +
            "-" +
            (this.months < 10 ? "0" + this.months : this.months) +
            "-" +
            (date < 10 ? "0" + date : date);
          this.week = this.calendarList[index].week;
          var pData = {
            date_Time: this.ClassKey,
            lnccode: this.lnc_Code,
          };
          //不需要号源时段的项目注释掉
          this.TeamSpans = "";
          var that = this;
          ajax
            .post(apiUrls.GetTeamSumTimeList, pData, { nocrypt: true })
            .then((r) => {
              if (r.data.success) {
                that.stateShow = true;
                var List = r.data.returnData;
                that.sumtimeList = List;
              }
            })
            .catch((e) => {
              Toast("系统异常！请联系管理员");
              return;
            });
          //
          return true;
        } else {
          this.ClassKey = false;
          return false;
        }
      } else {
        if (key == "充足" || key == "紧张") {
          this.years = this.current.year;
          this.months = this.current.month + 1;
          this.ClassKey =
            this.years +
            "-" +
            (this.months < 10 ? "0" + this.months : this.months) +
            "-" +
            (date < 10 ? "0" + date : date);
          this.week = this.calendarList[index].week;
          var pData = {
            date_Time: this.ClassKey,
            type: this.type,
            combs: this.combs,
          };
          //不需要号源时段的项目注释掉
          this.TimeSpan = "";
          var that = this;
          ajax
            .post(apiUrls.GetSumTimeList, pData, { nocrypt: true })
            .then((r) => {
              if (r.data.success) {
                // that.state = true;
                that.stateShow = true;
                var List = r.data.returnData;
                let sumList = [];
                for (let i = 0; i < List.length; i++) {
                  sumList.push({
                    sumtime_BegTime: List[i].sumtime_BegTime,
                    sumtime_Code: List[i].sumtime_Code,
                    sumtime_EndTime: List[i].sumtime_EndTime,
                    sumtime_Name: List[i].sumtime_Name,
                    team_Already: List[i].person_Already,
                    team_Sum: List[i].person_Sum,
                    team_Surplus: List[i].person_Surplus,
                  });
                }
                that.sumtimeList = sumList;
                console.log(that.sumtimeList);
              }
            })
            .catch((e) => {
              Toast("系统异常！请联系管理员");
              return;
            });
          //
          return true;
        } else {
          this.ClassKey = false;
          return false;
        }
      }
    },
    daybookCheckedClick: function (date, key, index) {
      if (this.type == "group" || this.type == "person") {
        // 判断能点击的日期是否为号源充足和紧张,休假，待开，""都不可点击
        if (key != "待开") {
          this.years = this.current.year;
          this.months = this.current.month + 1;
          this.ClassKey =
            this.years +
            "-" +
            (this.months < 10 ? "0" + this.months : this.months) +
            "-" +
            (date < 10 ? "0" + date : date);
          this.week = this.calendarList[index].week;
          var pData = {
            date_Time: this.ClassKey,
            lnccode: this.lnc_Code,
          };
          //不需要号源时段的项目注释掉
          this.TeamSpans = "";
          var that = this;
          ajax
            .post(apiUrls.GetTeamSumTimeList, pData, { nocrypt: true })
            .then((r) => {
              if (r.data.success) {
                that.stateShow = true;
                var List = r.data.returnData;
                that.sumtimeList = List;
              }
            })
            .catch((e) => {
              Toast("系统异常！请联系管理员");
              return;
            });
          //
          return true;
        } else {
          this.ClassKey = false;
          return false;
        }
      } else {
        if (key != "待开") {
          this.years = this.current.year;
          this.months = this.current.month + 1;
          this.ClassKey =
            this.years +
            "-" +
            (this.months < 10 ? "0" + this.months : this.months) +
            "-" +
            (date < 10 ? "0" + date : date);
          this.week = this.calendarList[index].week;
          var pData = {
            date_Time: this.ClassKey,
            type: this.type,
            combs: this.combs,
          };
          //不需要号源时段的项目注释掉
          this.TimeSpan = "";
          var that = this;
          ajax
            .post(apiUrls.GetSumTimeList, pData, { nocrypt: true })
            .then((r) => {
              if (r.data.success) {
                // that.state = true;
                that.stateShow = true;
                var List = r.data.returnData;
                let sumList = [];
                for (let i = 0; i < List.length; i++) {
                  sumList.push({
                    sumtime_BegTime: List[i].sumtime_BegTime,
                    sumtime_Code: List[i].sumtime_Code,
                    sumtime_EndTime: List[i].sumtime_EndTime,
                    sumtime_Name: List[i].sumtime_Name,
                    team_Already: List[i].person_Already,
                    team_Sum: List[i].person_Sum,
                    team_Surplus: List[i].person_Surplus,
                  });
                }
                that.sumtimeList = sumList;
                console.log(that.sumtimeList);
              }
            })
            .catch((e) => {
              Toast("系统异常！请联系管理员");
              return;
            });
          //
          return true;
        } else {
          console.log("123456");
          this.ClassKey = false;
          this.TeamSpans = "";
          this.sumtime_Name = "";
          this.stateShow = false;
          return false;
        }
      }
    },
    //获取所有套餐信息
    GetClusList() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAllClusList)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          let data = r.data.returnData;
          // let tableCopyTableList = data.map((val) => {
          //   switch (val.clus_sex) {
          //     case "1":
          //       val.clus_sex = "男";
          //       break;
          //     case "0":
          //       val.clus_sex = "女";
          //       break;
          //     case "%":
          //       val.clus_sex = "不限";
          //       break;
          //     default:
          //       break;
          //   }
          //   switch (val.clusType) {
          //     case "01":
          //       val.clusType = "健康体检";
          //       break;
          //     case "02":
          //       val.clusType = "职业体检";
          //       break;
          //     case "03":
          //       val.clusType = "从业体检";
          //       break;
          //     case "04":
          //       val.clusType = "招工体检";
          //       break;
          //     case "05":
          //       val.clusType = "学生体检";
          //       break;
          //     case "06":
          //       val.clusType = "征兵体检";
          //       break;
          //     default:
          //       break;
          //   }
          //   return val;
          // });
          that.tableAllClusData = data;
          // this.getGroup();
        })
        .catch((err) => {
          alert("获取套餐失败,请稍后重试");
        });
    },
    screenClusData(val) {
      var that = this;
      console.log(val);
      let clusIds = val.split(",");
      that.tableClusData = that.tableAllClusData.filter((item) => {
        for (let i = 0; i < clusIds.length; i++) {
          if (clusIds[i] == item.id) {
            return item;
          }
        }
        //筛选
        // return val.includes(item.id);
      });
    },
    //获取分类
    getGroup() {
      // 使用 Set 来删除重复项
      let uniqueData = [...new Set(this.tableData)];
      let grouped = uniqueData.reduce((result, item) => {
        let key = item.group;
        result[key] = result[key] || [];
        result[key].push(item);
        return result;
      }, {});
      let filterData = [];
      Object.keys(grouped).forEach((key) => {
        let filter = {};
        filter.text = key;
        filter.value = key;
        filterData.push(filter);
        // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
      });
      this.filters = filterData;
    },
    SyncOrderByOrderIds(id) {
      let idArr = [id];
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      let confirmMsg = "导入内院后，用户将无法取消订单, 是否继续?";
      this.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.SyncOrderByOrderIds, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.GetUserInfo();
            this.$message.success("同步到内院成功!");
            this.SMSdialogVisible = false;
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },

    //传入一个项目数组字符串 返回对应的项目名称
    getClusName(clusIds) {
      clusArr = clusIds.split(",");

      clusArr.forEach((item) => {

        console.log(item);
      });
    }
  },
};
</script>

<style lang="scss">
.useDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  font-size: 18px !important;
}

.lncTop {
  width: 100%;
  margin-top: 10px;
  display: flex;
}

.getNew {
  width: 180px;
}

.getNew .el-input__inner {
  padding: 0 5px !important;
  text-align: center;
  width: 180px !important;
}

#lncMid {
  width: 100%;
  margin-top: 0px !important;
  font-size: 16px;
}

.pageNation {
  margin-top: 20px;
  font-family: "Times New Roman", Times, serif;
}

.pageNation .el-pagination {
  display: flex;
  justify-content: flex-end;
}

.OrderDe span:nth-child(2) {
  color: blue;
}

.OrderDe .OrderDein {
  margin-top: 8px;
  margin-left: 20px;
  font-family: "Avenir", Helvetica, Arial, sans-serif;
}

.OrderDe span:first-child {
  /* 你的样式 */
  width: 150px !important;
}

.DYConfirmDiv {
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  margin-top: 13px;
  margin-left: 13px;
  font-size: 18px;
}

.DYConfirmSpan {
  margin-left: 8px;
  color: rgb(224, 103, 22);
}

.DYConfirmDiv div {
  margin-top: 3px;
  font-size: 16px;
}
</style>
