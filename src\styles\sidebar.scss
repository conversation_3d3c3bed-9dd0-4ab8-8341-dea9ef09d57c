#app {
  // 主体区域
  .main-container {
    min-height: 100vh;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin-left: 185px;
    background-color: #f5f7fa;
  }

  .main-containers {
    min-height: 100vh;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin-left: 64px;
    background-color: #f5f7fa;
  }

  // 侧边栏基础样式
  .sidebar-container,
  .sidebar-containers {
    height: 100vh;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;

    // 展开状态
    &.sidebar-container {
      width: 185px;
    }

    // 折叠状态
    &.sidebar-containers {
      width: 64px;
    }

    a {
      display: inline-block;
      width: 100%;
      text-decoration: none;
      color: inherit;
    }

    .svg-icon {
      margin-right: 12px;
      font-size: 18px;
      color: #606266;
      transition: color 0.2s ease;
    }

    .el-menu {
      border: none;
      background: transparent;
      width: 100%;

      // 菜单项基础样式
      .el-menu-item,
      .el-submenu__title {
        height: 48px;
        line-height: 48px;
        padding: 0 16px;
        margin: 4px 8px;
        border-radius: 8px;
        transition: all 0.2s ease;
        color: #606266;
        font-weight: 500;

        &:hover {
          background-color: #f0f9ff;
          color: #1677ff;

          .svg-icon {
            color: #1677ff;
          }
        }

        &.is-active {
          background: linear-gradient(135deg, #1677ff 0%, #69b7ff 100%);
          color: #ffffff;
          box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);

          .svg-icon {
            color: #ffffff;
          }
        }
      }

      // 子菜单样式
      .el-submenu {
        .el-menu-item {
          margin: 2px 16px 2px 24px;
          padding-left: 32px;
          height: 40px;
          line-height: 40px;
          font-size: 14px;

          &:hover {
            background-color: #e6f0ff;
          }

          &.is-active {
            background: #1677ff;
            color: #ffffff;
          }
        }
      }
    }
  }
  // 折叠状态样式
  .hideSidebar {
    .sidebar-container,
    .sidebar-container .el-menu {
      width: 64px;
    }

    .main-container {
      margin-left: 64px;
    }
  }

  // 折叠状态下的菜单项样式
  .el-menu--collapse {
    width: 64px;

    .el-menu-item,
    .el-submenu__title {
      padding: 0 !important;
      text-align: center;
      position: relative;

      span {
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .svg-icon {
        margin-right: 0;
        font-size: 20px;
      }

      // 折叠状态下的悬浮提示
      &:hover {
        &::after {
          content: attr(data-title);
          position: absolute;
          left: 70px;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(0, 0, 0, 0.8);
          color: #ffffff;
          padding: 8px 12px;
          border-radius: 6px;
          font-size: 12px;
          white-space: nowrap;
          z-index: 1002;
          opacity: 1;
          visibility: visible;
          transition: all 0.2s ease;

          // 小箭头
          &::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            border: 4px solid transparent;
            border-right-color: rgba(0, 0, 0, 0.8);
          }
        }
      }
    }

    .el-submenu {
      .el-submenu__title {
        .el-submenu__icon-arrow {
          display: none;
        }
      }

      // 折叠状态下隐藏子菜单
      .el-menu {
        display: none;
      }
    }
  }

  // 特殊处理：折叠状态下的子菜单悬浮显示
  .sidebar-containers {
    .el-submenu {
      position: relative;

      &:hover {
        .nest-menu {
          display: block !important;
          position: absolute;
          left: 64px;
          top: 0;
          min-width: 160px;
          background: #ffffff;
          box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.15);
          border-radius: 8px;
          z-index: 1002;
          padding: 8px 0;

          .el-menu-item {
            margin: 2px 8px;
            padding: 0 16px;
            text-align: left;

            span {
              opacity: 1;
              visibility: visible;
            }
          }
        }
      }
    }
    }

  // 响应式设计
  @media (max-width: 768px) {
    .main-container,
    .main-containers {
      margin-left: 0;
    }

    .sidebar-container,
    .sidebar-containers {
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &.mobile-show {
        transform: translateX(0);
      }
    }
  }

  // 侧边栏顶部logo区域
  .sidebar-logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;

    .logo-img {
      height: 32px;
      width: auto;
      transition: all 0.3s ease;
    }

    .logo-title {
      margin-left: 12px;
      font-size: 18px;
      font-weight: 600;
      color: #1677ff;
      transition: all 0.3s ease;
    }
  }

  // 折叠状态下隐藏logo文字
  .sidebar-containers .sidebar-logo {
    .logo-title {
      opacity: 0;
      width: 0;
      margin-left: 0;
      overflow: hidden;
    }
  }

  // 菜单项图标动画
  .el-menu-item,
  .el-submenu__title {
    .svg-icon,
    i[class*="el-icon-"] {
      transition: transform 0.2s ease;
    }

    &:hover {
      .svg-icon,
      i[class*="el-icon-"] {
        transform: scale(1.1);
      }
    }
  }

  // 加载动画
  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .sidebar-container,
  .sidebar-containers {
    animation: slideInLeft 0.3s ease-out;
  }

  // 菜单项进入动画
  .el-menu-item,
  .el-submenu {
    animation: fadeInUp 0.3s ease-out;
    animation-fill-mode: both;

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.05}s;
      }
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}