<template>
  <div class="login-wrap">
    <div class="login-bg-mask"></div>
    <div class="login-card">
      <div class="login-header">
        <img src="../../assets/img/HomeLogo.jpg" alt="医院Logo" class="login-logo" />
        <div class="login-title-group">
          <div class="login-title-main">深圳市龙岗中心医院</div>
          <div class="login-title-sub">体检预约后台管理系统</div>
        </div>
      </div>
      <div class="login-welcome">欢迎登录</div>
      <el-form :model="formModel" ref="login" label-width="0px" class="login-form">
        <el-form-item prop="username">
          <el-input v-model="formModel.user" placeholder="请输入账号" size="large" prefix-icon="el-icon-user" />
        </el-form-item>
        <el-form-item prop="password">
          <el-input show-password v-model="formModel.password" placeholder="请输入密码" size="large" prefix-icon="el-icon-lock" />
        </el-form-item>
        <el-form-item prop="checkCode">
          <el-row gutter="10">
            <el-col :span="15">
              <el-input placeholder="请输入验证码" v-model="formModel.checkCode" size="large" />
            </el-col>
            <el-col :span="9">
              <identify :contentWidth="96" :contentHeight="40" @changeCode="getCheckCode" ref="checkCodeImg"></identify>
            </el-col>
          </el-row>
        </el-form-item>
        <el-button type="primary" class="login-btn" size="large" @click="loadBtn()">登录</el-button>
      </el-form>
      <div class="login-footer">©2024 深圳市龙岗中心医院</div>
    </div>
  </div>
</template>
<script>
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import identify from "@/components/identify";
import { authService } from '../../service'

export default {
  components: {
    identify,
  },
  data() {
    return {
      formModel: {
        user: "",
        password: "",
        checkCode: ""
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" }
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        checkCode: [
          { required: true, message: "请输入验证码", trigger: "blur" }
        ]
      },
      checkNum: "" //验证码组件返回的随机数
    };
  },
  created() {
    //回车键登录 只在登录页面有效
    let that = this;
    document.onkeydown = function (e) {
      let keyCode = window.event.keyCode;
      if (that.$route.path == "/login" && keyCode == 13) {
        //验证在登录界面和按得键是回车键enter
        that.loadBtn(); //登录函数
      }
    };
  },
  methods: {
    loadBtn() {
      this.$store.commit("promission/resetAddRoutes"); //重新登陆重置AddRoutes保存的菜单数据
      //验证
      if (!this.formModel.user) {
        this.$message.warning("用户名不能为空");
        return;
      }
      if (!this.formModel.password) {
        this.$message.warning("密码不能为空");
        return;
      }
      if (this.formModel.checkCode != this.checkNum) {
        this.$message.warning("验证码不正确");

        //刷新验证码并清空输入
        this.refreshCode();
        return;
      }

      var pData = {
        Admin_Code: this.formModel.user,
        Admin_Pwd: this.formModel.password
      };
      ajax
        .post(apiUrls.login, pData, { nocrypt: true })
        .then(r => {
          console.log(r);
          if (!r.data.success) {
            this.$message.error(r.data.returnMsg);

            //刷新验证码并清空输入
            this.refreshCode();
            return;
          }
          this.$message.success("登录成功");
          storage.session.set("userRole", this.formModel.user);
          storage.session.set("user", JSON.stringify(r.data.returnData.admin));
          //设置token
          authService.setToken(r.data.returnData.token);
          this.$router.push({
            path: "/home"
          });
        })
        .catch(err => {
          this.$message.error("登录错误,请联系工作人员");

          //刷新验证码并清空输入
          this.refreshCode();
        });
    },
    //获取验证码返回的随机数
    getCheckCode(num) {
      this.checkNum = num;
    },
    //刷新验证码
    refreshCode() {
      this.$refs.checkCodeImg.handleClick();
      this.formModel.checkCode = "";
    }
  },
  mounted() { }
};
</script>
<style scoped>
.login-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  background: #f5f7fa;
  overflow: auto;
}
.login-bg-mask {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: url("../../assets/img/bg.png") center/cover no-repeat;
  filter: blur(2px) brightness(0.95);
  z-index: 0;
}
.login-card {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 380px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 6px 32px 0 rgba(22,119,255,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.06);
  padding: 40px 36px 24px 36px;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.login-logo {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(22,119,255,0.08);
}
.login-title-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.login-title-main {
  font-size: 22px;
  font-weight: 600;
  color: #1677ff;
  line-height: 1.2;
}
.login-title-sub {
  font-size: 14px;
  color: #333;
  margin-top: 2px;
}
.login-welcome {
  width: 100%;
  text-align: left;
  font-size: 16px;
  color: #888;
  margin-bottom: 18px;
  margin-top: 2px;
}
.login-form {
  width: 100%;
  margin-bottom: 12px;
}
.el-form-item {
  margin-bottom: 18px;
}
.login-btn {
  width: 100%;
  height: 40px;
  border-radius: 6px;
  background: #1677ff;
  border: none;
  font-size: 16px;
  font-weight: 500;
  margin-top: 6px;
}
.login-btn:hover {
  background: #409eff;
}
.login-footer {
  width: 100%;
  text-align: center;
  color: #bbb;
  font-size: 12px;
  margin-top: 18px;
  letter-spacing: 1px;
}
@media (max-width: 480px) {
  .login-card {
    width: 96vw;
    min-width: 0;
    padding: 24px 8vw 16px 8vw;
  }
  .login-logo {
    width: 40px;
    height: 40px;
  }
  .login-title-main {
    font-size: 18px;
  }
}
</style>