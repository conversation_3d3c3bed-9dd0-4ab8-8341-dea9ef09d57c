<template>
  <div>
    <div class="lncDiv">
      <!-- <div style="width: 100%">信息汇总</div> -->
      <div class="lncMid">
        <div class="topCard" v-if="false">
          <div class="contentCard">
            总号源汇总
            <img src="../../assets/img/team.png" alt="" style="width: 3rem; height: 3rem" />
          </div>
          <div class="contentCard" style="background-color: #42cac0">
            订单汇总
            <img src="../../assets/img/iconOrder.png" alt="" style="width: 3rem; height: 3rem" />
          </div>
        </div>
        <!-- 日期选择和统计概览 -->
        <div class="statistics-header">
          <div class="date-selector">
            <span class="demonstration">选择日期</span>
            <el-date-picker
              v-model="checktime"
              type="date"
              @change="handleDateChange"
              :clearable="false"
              placeholder="选择日期">
            </el-date-picker>
          </div>

          <!-- 统计卡片 -->
          <div class="stats-cards">
            <div class="stat-card">
              <div class="stat-number">{{ allSum.am.already + allSum.pm.already }}</div>
              <div class="stat-label">总预约人数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ allSum.am.sum + allSum.pm.sum }}</div>
              <div class="stat-label">总号源数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ (allSum.am.surplus || 0) + (allSum.pm.surplus || 0) }}</div>
              <div class="stat-label">剩余号源</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ mealsNum }}</div>
              <div class="stat-label">备餐数量</div>
            </div>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <!-- 号源统计饼图 -->
            <div class="chart-container">
              <div class="chart-title">
                <i class="el-icon-pie-chart"></i>
                {{ $dayjs(checktime).format("YYYY-MM-DD") }} 号源分布统计
              </div>
              <div id="sourceDistributionChart" class="chart-content"></div>
            </div>

            <!-- 时段对比柱状图 -->
            <div class="chart-container">
              <div class="chart-title">
                <i class="el-icon-s-data"></i>
                上午/下午时段对比
              </div>
              <div id="timeComparisonChart" class="chart-content"></div>
            </div>

            <div class="grid-content bg-purple">
              <!-- 总号源汇总表格 -->
              <div class="table-title">
                <i class="el-icon-document"></i>
                号源详细统计
              </div>
              <div class="allsum">
                <div class="allsum-item header-row">
                  <span>时段</span>
                  <span>总号源</span>
                  <span>已约</span>
                  <span>剩余</span>
                  <span>使用率</span>
                </div>
                <div class="allsum-item">
                  <span>{{ allSum.am.title }}</span>
                  <span>{{ allSum.am.sum }}</span>
                  <span>{{ allSum.am.already }}</span>
                  <span>{{ allSum.am.surplus || 0 }}</span>
                  <span class="usage-rate">{{ getUsageRate(allSum.am.already, allSum.am.sum) }}%</span>
                </div>
                <div class="allsum-item">
                  <span>{{ allSum.pm.title }}</span>
                  <span>{{ allSum.pm.sum }}</span>
                  <span>{{ allSum.pm.already }}</span>
                  <span>{{ allSum.pm.surplus || 0 }}</span>
                  <span class="usage-rate">{{ getUsageRate(allSum.pm.already, allSum.pm.sum) }}%</span>
                </div>
              </div>
            </div>
       
          </el-col>
          <el-col :span="12">
            <!-- 体检类型分布饼图 -->
            <div class="chart-container">
              <div class="chart-title">
                <i class="el-icon-pie-chart"></i>
                体检类型分布统计
              </div>
              <div id="examTypeChart" class="chart-content"></div>
            </div>

            <!-- 项目统计树状图 -->
            <div class="chart-container">
              <div class="chart-title">
                <i class="el-icon-share"></i>
                检查项目层级统计
              </div>
              <div id="projectTreeChart" class="chart-content"></div>
            </div>

            <!-- 体检订单详细表格 -->
            <div class="table-container">
              <div class="table-title">
                <i class="el-icon-document"></i>
                {{ $dayjs(checktime).format("YYYY-MM-DD") }}体检订单统计 ({{ ordersonlistcont }}人)
              </div>
              <el-table :data="tableDataOrder" stripe border style="width: 100%" class="modern-table">
                <el-table-column prop="type" :resizable="false" label="体检类型" min-width="120">
                  <template slot-scope="scope">
                    <el-tag :type="getExamTypeColor(scope.row.type)" size="small">
                      {{ Ordersontype(scope.row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" :resizable="false" label="订单数(人)" min-width="100">
                  <template slot-scope="scope">
                    <span class="quantity-badge">{{ scope.row.quantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="占比" min-width="80">
                  <template slot-scope="scope">
                    <span class="percentage">{{ getPercentage(scope.row.quantity, ordersonlistcont) }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
       
           
          </el-col>
     
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax } from "../../common/ajax";
import apiUrls from "../../config/apiUrls";
export default {
  name: "home",
  data() {
    return {
      tableDataCombByHC:[],
      tableDataCombByCT:[],
      checktime: new Date(),
      tableData: [],
      lncList: [],
      tableDataGj: [],
      teamlistcont: 0, //预约总人数
      teamafternoon: 0, //下午预约人数
      teammorningCont: 0, //上午预约人数
      personlistcont: 0,
      personafternoon: 0,
      personmorningCont: 0,
      mealsNum: 0,
      spanArr: [], // 记录每一行的合并数
      personspanArr: [], // 记录每一行的合并数
      pos: 0, // 记录索引
      posperson: 0, // 记录索引
      pieData: [], //饼图数据
      chartPie: null, //团检饼图
      chartPiePerson: null, //个检饼图
      teampieData: [],
      pieDataPerson: [], //个检饼图数据
      personpieData: [],
      tableDataOrder: [],
      ordersonlistcont: 0,
      ordersonspanArr: [], // 记录每一行的合并数
      tableDataComb: [],
      combsonlistcont: 0,
      combsonlistcontByCT: 0,
      combsonlistcontByHC: 0,
      allSum: {
        am: {
          title: "上午",
          sum: 0,
          already: 0,
          surplus: 0,
        },
        pm: {
          title: "下午",
          sum: 0,
          already: 0,
          surplus: 0,
        },
      },
      // 新增图表实例
      sourceDistributionChart: null,
      timeComparisonChart: null,
      examTypeChart: null,
      projectTreeChart: null,
    };
  },
  created() {
    this.GetGroupSum();
    this.GetPersonSum();
    this.GetOrderList();
    this.GetOrderListByContainsComb();
    this.GetNumberOfMeals();
    this.GetOrderListByContainsCombByCt();
    this.GetOrderListByContainsCombByHC();
    // this.$nextTick(() => {
    //     //   this.drawPie();
    //     this.drawPiePerson();
    // })
  },
  mounted() {
    this.spanArr = [];
    this.getSpanArr(this.tableData, "1");
    this.personspanArr = [];
    this.getSpanArr(this.tableDataGj, "2");

    // 初始化图表
    this.$nextTick(() => {
      this.initCharts();
    });
  },
  watch: {
    //团检预约数
    tableData: function (val, odlval) {
      let num = 0;
      this.teamlistcont = 0;
      this.teammorningCont = 0;
      this.teamafternoon = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        num += this.tableData[i].team_Already;
        this.teamlistcont = num;
        if (this.tableData[i].startTime == "上午") {
          this.teammorningCont += this.tableData[i].team_Already;
        }
        if (this.tableData[i].startTime == "下午") {
          this.teamafternoon += this.tableData[i].team_Already;
        }
      }
    },
    //个检预约数
    tableDataGj: function (val, odlval) {
      let num = 0;
      this.personmorningCont = 0;
      this.personafternoon = 0;
      this.personlistcont = 0;
      for (let i = 0; i < this.tableDataGj.length; i++) {
        num += this.tableDataGj[i].person_Already;
        this.personlistcont = num;
        if (this.tableDataGj[i].startTime == "上午") {
          this.personmorningCont += this.tableDataGj[i].person_Already;
        }
        if (this.tableDataGj[i].startTime == "下午") {
          this.personafternoon += this.tableDataGj[i].person_Already;
        }
      }
    },
    tableDataOrder: function () {
      let num = 0;
      this.ordersonlistcont = 0;
      for (let i = 0; i < this.tableDataOrder.length; i++) {
        num += parseInt(this.tableDataOrder[i].quantity);
      }
      this.ordersonlistcont = num;

      // 更新体检类型图表
      this.$nextTick(() => {
        this.updateExamTypeChart();
      });
    },
    tableDataComb: function () {
      let num = 0;
      this.combsonlistcont = 0;
      for (let i = 0; i < this.tableDataComb.length; i++) {
        num += parseInt(this.tableDataComb[i].quantity);
      }
      this.combsonlistcont = num;

      // 更新项目树状图
      this.$nextTick(() => {
        this.updateProjectTreeChart();
      });
    },
    tableDataCombByCT: function () {
      let num = 0;
      this.combsonlistcontByCT = 0;
      for (let i = 0; i < this.tableDataCombByCT.length; i++) {
        num += parseInt(this.tableDataCombByCT[i].quantity);
      }
      this.combsonlistcontByCT = num;

      // 更新项目树状图
      this.$nextTick(() => {
        this.updateProjectTreeChart();
      });
    },
    tableDataCombByHC: function () {
      let num = 0;
      this.combsonlistcontByHC = 0;
      for (let i = 0; i < this.tableDataCombByHC.length; i++) {
        num += parseInt(this.tableDataCombByHC[i].quantity);
      }
      this.combsonlistcontByHC = num;

      // 更新项目树状图
      this.$nextTick(() => {
        this.updateProjectTreeChart();
      });
    },

    // 监听allSum变化，更新相关图表
    allSum: {
      handler() {
        this.$nextTick(() => {
          this.updateSourceDistributionChart();
          this.updateTimeComparisonChart();
        });
      },
      deep: true
    },
  },
  methods: {
    handleDateChange() {
      this.allSum.am.sum = 0;
      this.allSum.am.already = 0;
      this.allSum.am.surplus = 0;
      this.allSum.pm.sum = 0;
      this.allSum.pm.already = 0;
      this.allSum.pm.surplus = 0;
      this.GetGroupSum();
      this.GetPersonSum();
      this.GetCombSum();
      this.GetOrderList();
      this.GetOrderListByContainsComb();
      this.GetNumberOfMeals();
      this.GetOrderListByContainsCombByCt();
      this.GetOrderListByContainsCombByHC();

      // 更新图表
      this.$nextTick(() => {
        this.updateCharts();
      });
    },

    // 初始化所有图表
    initCharts() {
      this.initSourceDistributionChart();
      this.initTimeComparisonChart();
      this.initExamTypeChart();
      this.initProjectTreeChart();
    },

    // 更新所有图表
    updateCharts() {
      this.updateSourceDistributionChart();
      this.updateTimeComparisonChart();
      this.updateExamTypeChart();
      this.updateProjectTreeChart();
    },

    // 初始化号源分布饼图
    initSourceDistributionChart() {
      const chartDom = document.getElementById('sourceDistributionChart');
      if (!chartDom) return;

      this.sourceDistributionChart = this.$echarts.init(chartDom);
      this.updateSourceDistributionChart();
    },

    // 更新号源分布饼图
    updateSourceDistributionChart() {
      if (!this.sourceDistributionChart) return;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '号源分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {
                value: this.allSum.am.already,
                name: '上午已约',
                itemStyle: { color: '#5470c6' }
              },
              {
                value: this.allSum.pm.already,
                name: '下午已约',
                itemStyle: { color: '#91cc75' }
              },
              {
                value: (this.allSum.am.surplus || 0) + (this.allSum.pm.surplus || 0),
                name: '剩余号源',
                itemStyle: { color: '#fac858' }
              }
            ]
          }
        ]
      };

      this.sourceDistributionChart.setOption(option);
    },

    // 初始化时段对比柱状图
    initTimeComparisonChart() {
      const chartDom = document.getElementById('timeComparisonChart');
      if (!chartDom) return;

      this.timeComparisonChart = this.$echarts.init(chartDom);
      this.updateTimeComparisonChart();
    },

    // 更新时段对比柱状图
    updateTimeComparisonChart() {
      if (!this.timeComparisonChart) return;

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['总号源', '已预约', '剩余'],
          top: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['上午', '下午'],
          axisLine: {
            lineStyle: {
              color: '#8392A5'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#8392A5'
            }
          }
        },
        series: [
          {
            name: '总号源',
            type: 'bar',
            data: [this.allSum.am.sum, this.allSum.pm.sum],
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '已预约',
            type: 'bar',
            data: [this.allSum.am.already, this.allSum.pm.already],
            itemStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '剩余',
            type: 'bar',
            data: [this.allSum.am.surplus || 0, this.allSum.pm.surplus || 0],
            itemStyle: {
              color: '#fac858'
            }
          }
        ]
      };

      this.timeComparisonChart.setOption(option);
    },

    // 初始化体检类型饼图
    initExamTypeChart() {
      const chartDom = document.getElementById('examTypeChart');
      if (!chartDom) return;

      this.examTypeChart = this.$echarts.init(chartDom);
      this.updateExamTypeChart();
    },

    // 更新体检类型饼图
    updateExamTypeChart() {
      if (!this.examTypeChart) return;

      const data = this.tableDataOrder.map(item => ({
        value: parseInt(item.quantity),
        name: this.Ordersontype(item.type),
        itemStyle: {
          color: this.getExamTypeChartColor(item.type)
        }
      }));

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}人 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '体检类型',
            type: 'pie',
            radius: '55%',
            center: ['60%', '50%'],
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };

      this.examTypeChart.setOption(option);
    },

    // 初始化项目树状图
    initProjectTreeChart() {
      const chartDom = document.getElementById('projectTreeChart');
      if (!chartDom) return;

      this.projectTreeChart = this.$echarts.init(chartDom);
      this.updateProjectTreeChart();
    },

    // 更新项目树状图
    updateProjectTreeChart() {
      if (!this.projectTreeChart) return;

      // 构建树状数据
      const treeData = {
        name: '检查项目统计',
        children: [
          {
            name: '彩超项目',
            value: this.combsonlistcont,
            children: this.tableDataComb.map(item => ({
              name: item.comb_Name,
              value: parseInt(item.quantity)
            }))
          },
          {
            name: 'CT项目',
            value: this.combsonlistcontByCT,
            children: this.tableDataCombByCT.map(item => ({
              name: item.comb_Name,
              value: parseInt(item.quantity)
            }))
          },
          {
            name: '核磁项目',
            value: this.combsonlistcontByHC,
            children: this.tableDataCombByHC.map(item => ({
              name: item.comb_Name,
              value: parseInt(item.quantity)
            }))
          }
        ]
      };

      const option = {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: function(params) {
            return params.name + ': ' + (params.value || 0) + '项/次';
          }
        },
        series: [
          {
            type: 'tree',
            data: [treeData],
            top: '5%',
            left: '7%',
            bottom: '2%',
            right: '20%',
            symbolSize: 7,
            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 12
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            },
            emphasis: {
              focus: 'descendant'
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      };

      this.projectTreeChart.setOption(option);
    },

    // 获取使用率
    getUsageRate(used, total) {
      if (total === 0) return 0;
      return Math.round((used / total) * 100);
    },

    // 获取百分比
    getPercentage(value, total) {
      if (total === 0) return 0;
      return Math.round((value / total) * 100);
    },

    // 获取体检类型颜色
    getExamTypeColor(type) {
      const colorMap = {
        'person': 'primary',
        'staff': 'success',
        'group': 'warning',
        'UD': 'danger'
      };
      return colorMap[type] || 'info';
    },

    // 获取体检类型图表颜色
    getExamTypeChartColor(type) {
      const colorMap = {
        'person': '#5470c6',
        'staff': '#91cc75',
        'group': '#fac858',
        'UD': '#ee6666'
      };
      return colorMap[type] || '#73c0de';
    },
    // 团检合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rows = this.spanArr[rowIndex];
        const cols = rows > 0 ? 1 : 0;
        return {
          rowspan: rows,
          colspan: cols,
        };
      }
    },
    //合并单元格
    objectSpanMethodGj({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rows = this.personspanArr[rowIndex];
        const cols = rows > 0 ? 1 : 0;
        return {
          rowspan: rows,
          colspan: cols,
        };
      }
    },
    //合并单元格
    objectSpanMethodOrder({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rows = this.ordersonspanArr[rowIndex];
        const cols = rows > 0 ? 1 : 0;
        return {
          rowspan: rows,
          colspan: cols,
        };
      }
    },
    //体检类型
    Persontype(typecode) {
      let typename = "";
      switch (typecode) {
        case "PersonalIndex":
          typename = "个人体检";
          break;
        case "Health":
          typename = "健康证体检";
          break;
        case "staff":
          typename = "入职体检";
          break;
        case "vehicle":
          typename = "驾驶证体检";
          break;
        case "Twocancer":
          typename = "两癌筛选";
          break;
        case "Civil":
          typename = "公务员体检";
          break;
        case "Personalized":
          typename = "个性化套餐";
          break;
        default:
          break;
      }
      return typename;
    },
    //体检类型
    Ordersontype(typecode) {
      let typename = "";
      switch (typecode) {
        case "person":
          typename = "个人套餐";
          break;
        case "staff":
          typename = "入职及其他";
          break;
        case "group":
          typename = "团体体检";
          break;
        case "UD":
          typename = "区干部体检";
          break;
        default:
          typename = "未识别";
          break;
      }
      return typename;
    },
    //单位名称
    lcnName(lcncore) {
      let lcnname = "";
      this.lncList.map((r) => {
        if (lcncore == r.lnc_Code) {
          lcnname = r.lnc_Name;
        }
      });
      return lcnname;
    },

    //备餐数量
    GetNumberOfMeals() {
      var that = this;
      that.mealsNum = 0;
      let pData = {
        begin_Time: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
      };
      ajax
        .post(apiUrls.GetNumberOfMeals, pData, { nocrypt: true })
        .then((r) => {
          console.log(r);
          that.mealsNum = r.data.returnData;
        });
    },
    //查询团检号源详情
    GetGroupSum() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
      };
      ajax.post(apiUrls.GetGroupSum, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.tableData = r.data.returnData;
        // this.lncList = r.data.returnData.lnclist
        this.getSpanArr(this.tableData, "1");

        r.data.returnData.forEach((e) => {
          if (e.startTime == "上午") {
            this.allSum.am.sum += e.team_Sum;
            this.allSum.am.already += e.team_Already;
            this.allSum.am.surplus += e.team_Surplus;
          } else if (e.startTime == "下午") {
            this.allSum.pm.sum += e.team_Sum;
            this.allSum.pm.already += e.team_Already;
            this.allSum.pm.surplus += e.team_Surplus;
          }
        });
      });
    },
    //特殊号源
    GetCombSum() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
      };
      ajax.post(apiUrls.GetCombSum, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        console.log(r.data);
        r.data.returnData.sumlist.forEach((e) => {
          if (e.startTime == "上午") {
            this.allSum.am.sum += e.team_Sum;
            this.allSum.am.already += e.team_Already;
            this.allSum.am.surplus += e.team_Surplus;
          } else if (e.startTime == "下午") {
            this.allSum.pm.sum += e.team_Sum;
            this.allSum.pm.already += e.team_Already;
            this.allSum.pm.surplus += e.team_Surplus;
          }
        });
      });
    },
    //個人号源
    GetPersonSum() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
      };
      ajax.post(apiUrls.GetPersonSum, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.tableDataGj = r.data.returnData;
        this.getSpanArr(this.tableDataGj, "2");
        r.data.returnData.forEach((e) => {
          if (e.startTime == "上午") {
            this.allSum.am.sum += e.person_Sum;
            this.allSum.am.already += e.person_Already;
            this.allSum.am.surplus += e.person_Surplus;
          } else if (e.startTime == "下午") {
            this.allSum.pm.sum += e.person_Sum;
            this.allSum.pm.already += e.person_Already;
            this.allSum.pm.surplus += e.person_Surplus;
          }
        });
      });
    },
    //订单
    GetOrderList() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
      };
      ajax.post(apiUrls.GetOrderList, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.tableDataOrder = r.data.returnData;
        this.getPbArr(this.tableDataOrder, "1");
      });
    },
    //彩超订单
    GetOrderListByContainsComb() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
        comb_Name: "彩超"
      };
      ajax
        .post(apiUrls.GetOrderListByContainsComb, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.tableDataComb = r.data.returnData;
          this.getPbArr(this.tableDataComb, "2");
        });
    },
    //CT订单
    GetOrderListByContainsCombByCt() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
        comb_Name: "CT"
      };
      ajax
        .post(apiUrls.GetOrderListByContainsComb, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.tableDataCombByCT = r.data.returnData;
          this.getPbArr(this.tableDataCombByCT, "2");
        });
    },

    //核磁订单
    GetOrderListByContainsCombByHC() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD"),
        comb_Name: "核磁"
      };
      ajax
        .post(apiUrls.GetOrderListByContainsComb, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.tableDataCombByHC = r.data.returnData;
          this.getPbArr(this.tableDataCombByHC, "2");
        });
    },
    
    //合并行数
    getSpanArr(data, type) {
      if (type == "1") {
        this.spanArr = [];
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            this.spanArr.push(1);
            this.pos = 0;
          } else {
            if (data[i].startTime === data[i - 1].startTime) {
              this.spanArr[this.pos] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.pos = i;
            }
          }
        }
      }
      if (type == "2") {
        this.personspanArr = [];
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            this.personspanArr.push(1);
            this.posperson = 0;
          } else {
            if (data[i].startTime === data[i - 1].startTime) {
              this.personspanArr[this.posperson] += 1;
              this.personspanArr.push(0);
            } else {
              this.personspanArr.push(1);
              this.posperson = i;
            }
          }
        }
      }
      // if (type == "3") {
      //   this.ordersonspanArr = []
      //   for (let i = 0; i < data.length; i++) {
      //     if (i === 0) {
      //       this.ordersonspanArr.push(1);
      //       this.posperson = 0;
      //     } else {
      //       if (data[i].startTime === data[i - 1].startTime) {
      //         this.ordersonspanArr[this.posperson] += 1;
      //         this.ordersonspanArr.push(0);
      //       } else {
      //         this.ordersonspanArr.push(1);
      //         this.posperson = i;
      //       }
      //     }
      //   }
      // }
    },
    getPbArr(data, type) {
      let num = 0;
      if (type == "1") {
        this.ordersonlistcont = 0;
        for (let i = 0; i < data.length; i++) {
          num += parseInt(data[i].quantity);
        }
        this.ordersonlistcont = num;
      }
      if (type == "2") {
        this.combsonlistcont = 0;
        for (let i = 0; i < data.length; i++) {
          num += parseInt(data[i].quantity);
        }
        this.combsonlistcont = num;
      }
    },
  },

  // 组件销毁时清理图表实例
  beforeDestroy() {
    if (this.sourceDistributionChart) {
      this.sourceDistributionChart.dispose();
    }
    if (this.timeComparisonChart) {
      this.timeComparisonChart.dispose();
    }
    if (this.examTypeChart) {
      this.examTypeChart.dispose();
    }
    if (this.projectTreeChart) {
      this.projectTreeChart.dispose();
    }
  }
};
</script>

<style lang="scss">
.lncDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;

  .lncTop {
    width: 100%;
    margin-top: 10px;
  }

  .lncMid {
    margin-top: 20px;
    width: 100%;

    .pageNation {
      margin-top: 10px;

      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

.el-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.allsum {
  width: 100%;
}

.allsum-item {
  display: flex;
  justify-content: space-between;

  border-bottom: 1px solid #f0f0f0;
  font-weight: bold;
}

.allsum-item span {
  width: 150px;
  text-align: left;
  padding: 0.8rem;
}

.el-col {
  border-radius: 4px;
  border: 1px solid #dddee1;
  background: #ffffff;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #ffffff;
}

.bg-purple-light {
  background: #ffffff;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
  margin: 1rem;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.title {
  border: 1px solid #f0f0f0;
  margin-bottom: 1rem;
  padding: 0.3rem;
  font-weight: 700;
}

.topCard {
  display: flex;

  .contentCard {
    width: 50%;
    background-color: #53bae2;
    //padding: 1.2rem;
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.8rem;
  }
}

.perContentCard {
  width: 100%;
  background-color: #53bae2;
  //padding: 1.2rem;
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem;
}

/* 新增样式 */
.statistics-header {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.date-selector {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-selector .demonstration {
  font-size: 16px;
  font-weight: 600;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 8px;
  color: #fff;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  color: #fff;
}

.chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-title i {
  color: #1677ff;
  font-size: 18px;
}

.chart-content {
  height: 300px;
  width: 100%;
}

.table-container {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-title i {
  color: #1677ff;
  font-size: 18px;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

.quantity-badge {
  display: inline-block;
  padding: 4px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  font-weight: 600;
  font-size: 12px;
}

.percentage {
  font-weight: 600;
  color: #1677ff;
}

.usage-rate {
  font-weight: 600;
  color: #52c41a;
}

.allsum-item.header-row {
  background: #f5f7fa;
  font-weight: 700;
  color: #333;
}

.allsum-item span {
  width: 120px;
  text-align: center;
  padding: 12px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-number {
    font-size: 2rem;
  }

  .chart-content {
    height: 250px;
  }
}
</style>
