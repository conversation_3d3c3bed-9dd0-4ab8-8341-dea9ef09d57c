<!--
 * @Author: 536584936 <EMAIL>
 * @Date: 2025-01-16 14:37:10
 * @LastEditors: 536584936 <EMAIL>
 * @LastEditTime: 2025-01-16 14:53:07
 * @FilePath: \龙岗中医院后台管理\src\views\TeamExamination\z.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="editClus">
    <div class="CreateComboForm">
      <el-form
              class="container-header-form"
              :inline="true"
              :label-position="'right'"
              label-width="80px"
            >
              <el-form-item label="套餐名称">
                <el-input
                  v-model="ClusList.clus_Name"
                  placeholder="请输入套餐名称"
                  size="small"
                ></el-input>
              </el-form-item>
              <el-form-item label="套餐售价">
                <el-input
                  v-model="ClusList.price"
                  placeholder="请输入套餐价格"
                  size="small"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="ClusList.lockPrice">锁定金额</el-checkbox>
              </el-form-item>
              <el-form-item label="性别">
                <el-select v-model="ClusList.clus_sex" size="small">
                  <el-option label="不限" value="%"></el-option>
                  <el-option label="男" value="1"></el-option>
                  <el-option label="女" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否启用">
                <el-select v-model="ClusList.state" size="small">
                  <el-option label="启用" value="T"></el-option>
                  <el-option label="禁用" value="F"></el-option>
                </el-select>
              </el-form-item>
              <div></div>
              <el-form-item label="业务类型">
                <el-select v-model="ClusList.business" size="small">
                  <el-option
                    v-for="item in YWoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="体检类别">
                <el-select v-model="ClusList.category" size="small">
                  <el-option
                    v-for="item in LBoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="改项限制">
                <el-select v-model="ClusList.isModify" size="small">
                  <el-option label="允许加项" value="T"></el-option>
                  <el-option label="允许加指定项目" value="N"></el-option>
                  <el-option label="不允许加项" value="F"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="限制金额" v-if="ClusList.isModify != 'F'">
                <el-input
                  v-model="ClusList.maxPrice"
                  placeholder="请输入限制金额"
                  size="small"
                ></el-input>
              </el-form-item>
              <div></div>
              <el-form-item label="套餐分类">
                <el-select v-model="ClusList.clusType" size="small">
                  <el-option label="个人健康体检" value="01"></el-option>
                  <el-option label="入职及其他" value="06"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="年龄限制">
                <el-select v-model="ClusList.ageIimit" size="small">
                  <el-option label="启用" value="T"></el-option>
                  <el-option label="禁用" value="F"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="限制范围" v-if="ClusList.ageIimit == 'T'">
                <el-input
                  v-model="ClusList.minAge"
                  placeholder="最小年龄"
                  type="number"
                  style="width: 100px"
                ></el-input>
                <span>至</span>
                <el-input
                  v-model="ClusList.maxAge"
                  placeholder="最大年龄"
                  type="number"
                  style="width: 100px"
                ></el-input>
              </el-form-item>
              <div></div>
              <el-form-item label="套餐简介">
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="ClusList.clus_Note"
                  placeholder="请输入套餐简介"
                ></el-input>
              </el-form-item>
              <div></div>
              <el-form-item label="注意事项">
                <editClusTinymce v-model="ClusList.Notice" />
              </el-form-item>
            </el-form>
    </div>
    
  </div>
</template>

<script>
export default{
  data(){
    return{
      //套餐模型
      ClusList: {
        id: "",
        clus_Code: "",
        clus_Name: "",
        state: "",
        price: "",
        isModify: "",
        maxPrice: 0,
        clus_sex: "",
        clus_Note: "",
        category: "",
        business: "",
        ageIimit: "",
        maxAge: "",
        minAge: "",
        clusType: "",
        Notice: "",
        lockPrice: false,
      },
    }
  }
}
</script>


<style>
  .CreateComboForm{
    background-color: #f2f2f2;
  }
  .el-form--inline .el-form-item{
    width: 350px!important;
    
   
  }
  
</style>
